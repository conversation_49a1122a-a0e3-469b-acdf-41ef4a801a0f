package helper

import (
	"database/sql"
	"errors"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// Test ValidateIdentifier function
func Test_ValidateIdentifier(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		identifier  string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid identifier",
			identifier:  "valid-org-123",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty identifier",
			identifier:  "",
			expectedErr: ErrInvalidIdentifier,
			wantErr:     true,
		},
		{
			name:        "whitespace only identifier",
			identifier:  "   ",
			expectedErr: ErrInvalidIdentifier,
			wantErr:     true,
		},
		{
			name:        "identifier with spaces",
			identifier:  "  valid-org-123  ",
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute the function under test
			err := ValidateIdentifier(tt.identifier)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_ValidateOrganizationID tests the ValidateOrganizationID function
func Test_ValidateOrganizationID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		orgID       string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid_organization_id",
			orgID:       "123e4567-e89b-12d3-a456-************",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_organization_id",
			orgID:       "",
			expectedErr: ErrInvalidOrganizationID,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_organization_id",
			orgID:       "   ",
			expectedErr: ErrInvalidOrganizationID,
			wantErr:     true,
		},
		{
			name:        "invalid_uuid_format",
			orgID:       "invalid-uuid-format",
			expectedErr: ErrInvalidOrganizationID,
			wantErr:     true,
		},
		{
			name:        "organization_id_with_spaces",
			orgID:       "  123e4567-e89b-12d3-a456-************  ",
			expectedErr: ErrInvalidOrganizationID,
			wantErr:     true,
		},
		{
			name:        "nil_uuid",
			orgID:       "00000000-0000-0000-0000-000000000000",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "different_valid_uuid",
			orgID:       "987fcdeb-51a2-43d1-b654-************",
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a test request with the organization ID
			req := httptest.NewRequest("GET", "/test", nil)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.orgID,
			})

			// Execute the function under test
			result, err := ValidateOrganizationID(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Equal(t, uuid.Nil, result)
			} else {
				assert.NoError(t, err)

				// Parse the expected UUID to compare
				expectedUUID, parseErr := uuid.Parse(tt.orgID)
				if parseErr == nil {
					assert.Equal(t, expectedUUID, result)
				}
			}
		})
	}
}

// Test_ValidateInviteID tests the ValidateInviteID function
func Test_ValidateInviteID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		inviteID    string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid_invite_id",
			inviteID:    "123e4567-e89b-12d3-a456-************",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_invite_id",
			inviteID:    "",
			expectedErr: ErrInvalidInviteID,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_invite_id",
			inviteID:    "   ",
			expectedErr: ErrInvalidInviteID,
			wantErr:     true,
		},
		{
			name:        "invalid_uuid_format",
			inviteID:    "invalid-uuid-format",
			expectedErr: ErrInvalidInviteID,
			wantErr:     true,
		},
		{
			name:        "invite_id_with_spaces",
			inviteID:    "  123e4567-e89b-12d3-a456-************  ",
			expectedErr: ErrInvalidInviteID,
			wantErr:     true,
		},
		{
			name:        "nil_uuid",
			inviteID:    "00000000-0000-0000-0000-000000000000",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "different_valid_uuid",
			inviteID:    "987fcdeb-51a2-43d1-b654-************",
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a test request with the invite ID
			req := httptest.NewRequest("GET", "/test", nil)
			req = mux.SetURLVars(req, map[string]string{
				"inviteId": tt.inviteID,
			})

			// Execute the function under test
			result, err := ValidateInviteID(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Equal(t, uuid.Nil, result)
			} else {
				assert.NoError(t, err)

				// Parse the expected UUID to compare
				expectedUUID, parseErr := uuid.Parse(tt.inviteID)
				if parseErr == nil {
					assert.Equal(t, expectedUUID, result)
				}
			}
		})
	}
}

// Test_ValidateUserID tests the ValidateUserID function
func Test_ValidateUserID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		userID      string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid_user_id",
			userID:      "123e4567-e89b-12d3-a456-************",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_user_id",
			userID:      "",
			expectedErr: ErrInvalidUserID,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_user_id",
			userID:      "   ",
			expectedErr: ErrInvalidUserID,
			wantErr:     true,
		},
		{
			name:        "invalid_uuid_format",
			userID:      "invalid-uuid-format",
			expectedErr: ErrInvalidUserID,
			wantErr:     true,
		},
		{
			name:        "user_id_with_spaces",
			userID:      "  123e4567-e89b-12d3-a456-************  ",
			expectedErr: ErrInvalidUserID,
			wantErr:     true,
		},
		{
			name:        "nil_uuid",
			userID:      "00000000-0000-0000-0000-000000000000",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "different_valid_uuid",
			userID:      "987fcdeb-51a2-43d1-b654-************",
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a test request with the user ID
			req := httptest.NewRequest("GET", "/test", nil)
			req = mux.SetURLVars(req, map[string]string{
				"userId": tt.userID,
			})

			// Execute the function under test
			result, err := ValidateUserID(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Equal(t, uuid.Nil, result)
			} else {
				assert.NoError(t, err)

				// Parse the expected UUID to compare
				expectedUUID, parseErr := uuid.Parse(tt.userID)
				if parseErr == nil {
					assert.Equal(t, expectedUUID, result)
				}
			}
		})
	}
}

// Test_ValidateAuthMethodID tests the ValidateAuthMethodID function
func Test_ValidateAuthMethodID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		authMethodID string
		expectedErr  error
		wantErr      bool
	}{
		{
			name:         "valid_auth_method_id",
			authMethodID: "123e4567-e89b-12d3-a456-************",
			expectedErr:  nil,
			wantErr:      false,
		},
		{
			name:         "empty_auth_method_id",
			authMethodID: "",
			expectedErr:  ErrInvalidAuthMethodID,
			wantErr:      true,
		},
		{
			name:         "whitespace_only_auth_method_id",
			authMethodID: "   ",
			expectedErr:  ErrInvalidAuthMethodID,
			wantErr:      true,
		},
		{
			name:         "invalid_uuid_format",
			authMethodID: "invalid-uuid-format",
			expectedErr:  ErrInvalidAuthMethodID,
			wantErr:      true,
		},
		{
			name:         "auth_method_id_with_spaces",
			authMethodID: "  123e4567-e89b-12d3-a456-************  ",
			expectedErr:  ErrInvalidAuthMethodID,
			wantErr:      true,
		},
		{
			name:         "nil_uuid",
			authMethodID: "00000000-0000-0000-0000-000000000000",
			expectedErr:  nil,
			wantErr:      false,
		},
		{
			name:         "different_valid_uuid",
			authMethodID: "987fcdeb-51a2-43d1-b654-************",
			expectedErr:  nil,
			wantErr:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a test request with the auth method ID
			req := httptest.NewRequest("GET", "/test", nil)
			req = mux.SetURLVars(req, map[string]string{
				"authMethodId": tt.authMethodID,
			})

			// Execute the function under test
			result, err := ValidateAuthMethodID(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Equal(t, uuid.Nil, result)
			} else {
				assert.NoError(t, err)

				// Parse the expected UUID to compare
				expectedUUID, parseErr := uuid.Parse(tt.authMethodID)
				if parseErr == nil {
					assert.Equal(t, expectedUUID, result)
				}
			}
		})
	}
}

// Test_ParseUUIDFromRequest tests the ParseUUIDFromRequest function
func Test_ParseUUIDFromRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		key          string
		urlVars      map[string]string
		expectedUUID uuid.UUID
		expectedErr  error
		wantErr      bool
	}{
		{
			name:         "valid_uuid",
			key:          "id",
			urlVars:      map[string]string{"id": "123e4567-e89b-12d3-a456-************"},
			expectedUUID: uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			expectedErr:  nil,
			wantErr:      false,
		},
		{
			name:         "different_valid_uuid",
			key:          "userId",
			urlVars:      map[string]string{"userId": "987fcdeb-51a2-43d1-b654-************"},
			expectedUUID: uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
			expectedErr:  nil,
			wantErr:      false,
		},
		{
			name:         "empty_value",
			key:          "id",
			urlVars:      map[string]string{"id": ""},
			expectedUUID: uuid.Nil,
			expectedErr:  ErrInvalidUUID,
			wantErr:      true,
		},
		{
			name:         "whitespace_only_value",
			key:          "id",
			urlVars:      map[string]string{"id": "   "},
			expectedUUID: uuid.Nil,
			expectedErr:  ErrInvalidUUID,
			wantErr:      true,
		},
		{
			name:         "key_not_found",
			key:          "nonexistent",
			urlVars:      map[string]string{"id": "123e4567-e89b-12d3-a456-************"},
			expectedUUID: uuid.Nil,
			expectedErr:  ErrInvalidUUID,
			wantErr:      true,
		},
		{
			name:         "invalid_uuid_format",
			key:          "id",
			urlVars:      map[string]string{"id": "invalid-uuid-format"},
			expectedUUID: uuid.Nil,
			expectedErr:  nil, // uuid.Parse returns a specific error, not ErrInvalidUUID
			wantErr:      true,
		},
		{
			name:         "nil_uuid_string",
			key:          "id",
			urlVars:      map[string]string{"id": "00000000-0000-0000-0000-000000000000"},
			expectedUUID: uuid.Nil,
			expectedErr:  nil,
			wantErr:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a test request with the URL variables
			req := httptest.NewRequest("GET", "/test", nil)
			req = mux.SetURLVars(req, tt.urlVars)

			// Execute the function under test
			result, err := ParseUUIDFromRequest(req, tt.key)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErr != nil {
					assert.Equal(t, tt.expectedErr, err)
				}
				assert.Equal(t, tt.expectedUUID, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedUUID, result)
			}
		})
	}
}

// Test_GenerateRandomTokenHex tests the GenerateRandomTokenHex function
func Test_GenerateRandomTokenHex(t *testing.T) {
	t.Parallel()

	// Mock the randRead function for testing
	originalRandRead := randRead
	defer func() { randRead = originalRandRead }()

	tests := []struct {
		name         string
		length       uint
		mockRandRead func([]byte) (int, error)
		expectedErr  error
		wantErr      bool
	}{
		{
			name:   "valid_length_even",
			length: 10,
			mockRandRead: func(b []byte) (int, error) {
				// Fill with some test data
				for i := range b {
					b[i] = byte(i)
				}
				return len(b), nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:   "valid_length_odd",
			length: 11,
			mockRandRead: func(b []byte) (int, error) {
				// Fill with some test data
				for i := range b {
					b[i] = byte(i)
				}
				return len(b), nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:   "zero_length",
			length: 0,
			mockRandRead: func(b []byte) (int, error) {
				return 0, nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:   "rand_read_error",
			length: 10,
			mockRandRead: func(b []byte) (int, error) {
				return 0, assert.AnError
			},
			expectedErr: assert.AnError,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up the mock
			if tt.mockRandRead != nil {
				randRead = tt.mockRandRead
			}

			// Execute the function under test
			result, err := GenerateRandomTokenHex(tt.length)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Empty(t, result)
			} else {
				assert.NoError(t, err)

				// Check that the result has the expected length
				// For even lengths, result should be exactly length characters
				// For odd lengths, result should be length-1 characters (since we need 2 hex chars per byte)
				expectedLen := tt.length
				if tt.length%2 == 1 {
					expectedLen = tt.length - 1
				}
				assert.Len(t, result, int(expectedLen))
			}
		})
	}
}

// Test_ValidateDeviceID tests the ValidateDeviceID function
func Test_ValidateDeviceID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		deviceID    string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid_device_id",
			deviceID:    "123e4567-e89b-12d3-a456-************",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_device_id",
			deviceID:    "",
			expectedErr: ErrInvalidDeviceID,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_device_id",
			deviceID:    "   ",
			expectedErr: ErrInvalidDeviceID,
			wantErr:     true,
		},
		{
			name:        "invalid_uuid_format",
			deviceID:    "invalid-uuid-format",
			expectedErr: ErrInvalidDeviceID,
			wantErr:     true,
		},
		{
			name:        "device_id_with_spaces",
			deviceID:    "  123e4567-e89b-12d3-a456-************  ",
			expectedErr: ErrInvalidDeviceID,
			wantErr:     true,
		},
		{
			name:        "nil_uuid",
			deviceID:    "00000000-0000-0000-0000-000000000000",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "different_valid_uuid",
			deviceID:    "987fcdeb-51a2-43d1-b654-************",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "uuid_with_uppercase",
			deviceID:    "123E4567-E89B-12D3-A456-************",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "uuid_with_mixed_case",
			deviceID:    "123e4567-E89b-12D3-a456-************",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "partial_uuid",
			deviceID:    "123e4567-e89b-12d3",
			expectedErr: ErrInvalidDeviceID,
			wantErr:     true,
		},
		{
			name:        "uuid_with_extra_chars",
			deviceID:    "123e4567-e89b-12d3-a456-************-extra",
			expectedErr: ErrInvalidDeviceID,
			wantErr:     true,
		},
		{
			name:        "uuid_with_special_chars",
			deviceID:    "123e4567-e89b-12d3-a456-42661417400@",
			expectedErr: ErrInvalidDeviceID,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a test request with the device ID
			req := httptest.NewRequest("GET", "/test", nil)
			req = mux.SetURLVars(req, map[string]string{
				"deviceId": tt.deviceID,
			})

			// Execute the function under test
			result, err := ValidateDeviceID(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Equal(t, uuid.Nil, result)
			} else {
				assert.NoError(t, err)

				// Parse the expected UUID to compare
				expectedUUID, parseErr := uuid.Parse(tt.deviceID)
				if parseErr == nil {
					assert.Equal(t, expectedUUID, result)
				}
			}
		})
	}
}

// Test_ValidateRoleExistsAndBelongsToOrganization covers role existence validation
func Test_ValidateRoleExistsAndBelongsToOrganization(t *testing.T) {
	t.Parallel()

	roleID := uuid.New()
	orgID := uuid.New()

	cases := []struct {
		name      string
		setupMock func() connect.DatabaseExecutor
		wantErr   error
	}{
		{
			name: "success_role_exists_true",
			setupMock: func() connect.DatabaseExecutor {
				m := &dbexecutor.FakeDBExecutor{}
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"exists": true}, nil
				}
				return m
			},
			wantErr: nil,
		},
		{
			name: "db_error",
			setupMock: func() connect.DatabaseExecutor {
				m := &dbexecutor.FakeDBExecutor{}
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, errors.New("boom")
				}
				return m
			},
			wantErr: ErrDatabaseOperation,
		},
		{
			name: "missing_exists_column",
			setupMock: func() connect.DatabaseExecutor {
				m := &dbexecutor.FakeDBExecutor{}
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{}, nil
				}
				return m
			},
			wantErr: ErrDatabaseOperation,
		},
		{
			name: "invalid_exists_type",
			setupMock: func() connect.DatabaseExecutor {
				m := &dbexecutor.FakeDBExecutor{}
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"exists": 1}, nil
				}
				return m
			},
			wantErr: ErrDatabaseOperation,
		},
		{
			name: "role_not_found",
			setupMock: func() connect.DatabaseExecutor {
				m := &dbexecutor.FakeDBExecutor{}
				m.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"exists": false}, nil
				}
				return m
			},
			wantErr: ErrRoleNotFound,
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			err := ValidateRoleExistsAndBelongsToOrganization(tc.setupMock(), roleID, orgID)
			if tc.wantErr == nil {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tc.wantErr)
			}
		})
	}
}

// Test_ValidateSoftwareGateWayBelongsToOrganization tests the ValidateSoftwareGateWayBelongsToOrganization function
func Test_ValidateSoftwareGateWayBelongsToOrganization(t *testing.T) {
	t.Parallel()

	// Test setup

	tests := []struct {
		name               string
		organizationId     uuid.UUID
		softwareGatewayId  uuid.UUID
		mockQueryRowStruct func(dest interface{}, query string, args ...interface{}) error
		expectedErr        error
		wantErr            bool
	}{
		{
			name:              "valid_software_gateway_belongs_to_organization",
			organizationId:    uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			softwareGatewayId: uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// Mock successful query result
				if destStruct, ok := dest.(*struct{ Exists int }); ok {
					destStruct.Exists = 1
				}
				return nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:              "software_gateway_not_found",
			organizationId:    uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			softwareGatewayId: uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrNoRows
			},
			expectedErr: ErrSoftwareGatewayNotFound,
			wantErr:     true,
		},
		{
			name:              "database_operation_failed",
			organizationId:    uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			softwareGatewayId: uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrConnDone
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:              "nil_uuids",
			organizationId:    uuid.Nil,
			softwareGatewayId: uuid.Nil,
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// Mock successful query result for nil UUIDs
				if destStruct, ok := dest.(*struct{ Exists int }); ok {
					destStruct.Exists = 1
				}
				return nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:              "different_valid_uuids",
			organizationId:    uuid.MustParse("11111111-1111-1111-1111-111111111111"),
			softwareGatewayId: uuid.MustParse("*************-2222-2222-************"),
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// Mock successful query result
				if destStruct, ok := dest.(*struct{ Exists int }); ok {
					destStruct.Exists = 1
				}
				return nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: tt.mockQueryRowStruct,
			}

			// Execute the function under test
			err := ValidateSoftwareGateWayBelongsToOrganization(mockDB, tt.organizationId, tt.softwareGatewayId)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify the mock was called
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount, "QueryRowStruct should be called exactly once")
		})
	}
}

// Test_ValidateLocationBelongsToOrganization tests the ValidateLocationBelongsToOrganization function
func Test_ValidateLocationBelongsToOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name               string
		organizationId     uuid.UUID
		locationId         uuid.UUID
		mockQueryRowStruct func(dest interface{}, query string, args ...interface{}) error
		expectedErr        error
		wantErr            bool
	}{
		{
			name:           "valid_location_belongs_to_organization",
			organizationId: uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			locationId:     uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// Mock successful query result
				if destStruct, ok := dest.(*struct{ Exists int }); ok {
					destStruct.Exists = 1
				}
				return nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:           "location_not_found",
			organizationId: uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			locationId:     uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrNoRows
			},
			expectedErr: ErrLocationNotFound,
			wantErr:     true,
		},
		{
			name:           "database_operation_failed",
			organizationId: uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			locationId:     uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrConnDone
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:           "nil_uuids",
			organizationId: uuid.Nil,
			locationId:     uuid.Nil,
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// Mock successful query result for nil UUIDs
				if destStruct, ok := dest.(*struct{ Exists int }); ok {
					destStruct.Exists = 1
				}
				return nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:           "different_valid_uuids",
			organizationId: uuid.MustParse("11111111-1111-1111-1111-111111111111"),
			locationId:     uuid.MustParse("*************-2222-2222-************"),
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// Mock successful query result
				if destStruct, ok := dest.(*struct{ Exists int }); ok {
					destStruct.Exists = 1
				}
				return nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:           "query_returns_zero_exists",
			organizationId: uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			locationId:     uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// Mock query returning 0 exists (should still be successful)
				if destStruct, ok := dest.(*struct{ Exists int }); ok {
					destStruct.Exists = 0
				}
				return nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:           "custom_database_error",
			organizationId: uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			locationId:     uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrTxDone
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:           "connection_error",
			organizationId: uuid.MustParse("123e4567-e89b-12d3-a456-************"),
			locationId:     uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrConnDone
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: tt.mockQueryRowStruct,
			}

			// Execute the function under test
			err := ValidateLocationBelongsToOrganization(mockDB, tt.organizationId, tt.locationId)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify the mock was called
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount, "QueryRowStruct should be called exactly once")
		})
	}
}

// Test_ValidateLocationBelongsToOrganization_QueryValidation tests query structure
func Test_ValidateLocationBelongsToOrganization_QueryValidation(t *testing.T) {
	t.Parallel()

	organizationId := uuid.MustParse("123e4567-e89b-12d3-a456-************")
	locationId := uuid.MustParse("987fcdeb-51a2-43d1-b654-************")

	var capturedQuery string
	var capturedArgs []interface{}

	// Create mock database executor that captures query and args
	mockDB := &dbexecutor.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			capturedQuery = query
			capturedArgs = args
			// Mock successful query result
			if destStruct, ok := dest.(*struct{ Exists int }); ok {
				destStruct.Exists = 1
			}
			return nil
		},
	}

	// Execute the function under test
	err := ValidateLocationBelongsToOrganization(mockDB, organizationId, locationId)

	// Assert results
	assert.NoError(t, err)

	// Validate query structure
	assert.Contains(t, capturedQuery, "SELECT 1 FROM {{Location}}")
	assert.Contains(t, capturedQuery, "WHERE Id = $1 AND OrganizationId = $2")
	assert.Len(t, capturedArgs, 2, "Should have exactly 2 arguments")
	assert.Equal(t, locationId, capturedArgs[0], "First argument should be locationId")
	assert.Equal(t, organizationId, capturedArgs[1], "Second argument should be organizationId")

	// Verify the mock was called
	assert.Equal(t, 1, mockDB.QueryRowStructCallCount, "QueryRowStruct should be called exactly once")
}

// Test_ValidateOrganizationExistedOrDeleted tests the ValidateOrganizationExistedOrDeleted function
func Test_ValidateOrganizationExistedOrDeleted(t *testing.T) {
	t.Parallel()

	// Create a test UUID
	testOrgID := uuid.New()

	tests := []struct {
		name           string
		organizationID uuid.UUID
		mockDB         func() connect.DatabaseExecutor
		expectedErr    error
		wantErr        bool
	}{
		{
			name:           "organization_exists_and_not_deleted",
			organizationID: testOrgID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock successful query with organization found - set Exists to true
						if orgStruct, ok := dest.(*struct {
							Exists bool `db:"exists"`
						}); ok {
							orgStruct.Exists = true
						}
						return nil
					},
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:           "organization_not_found",
			organizationID: testOrgID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock query with organization not found - set Exists to false
						if orgStruct, ok := dest.(*struct {
							Exists bool `db:"exists"`
						}); ok {
							orgStruct.Exists = false
						}
						return nil
					},
				}
			},
			expectedErr: ErrOrganizationNotFound,
			wantErr:     true,
		},
		{
			name:           "database_operation_error",
			organizationID: testOrgID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock database error
						return assert.AnError
					},
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up the mock database
			mockDB := tt.mockDB()

			// Execute the function under test
			err := ValidateOrganizationExistedOrDeleted(mockDB, tt.organizationID)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_GetMembershipsByUserID tests the GetMembershipsByUserID function
func Test_GetMembershipsByUserID(t *testing.T) {
	t.Parallel()

	// Create test UUIDs
	testUserID := uuid.New()
	testOrgID := uuid.New()
	testMembershipID1 := uuid.New()
	testMembershipID2 := uuid.New()

	tests := []struct {
		name           string
		userID         uuid.UUID
		organizationID uuid.UUID
		mockDB         func() connect.DatabaseExecutor
		expected       []uuid.UUID
		expectedErr    error
		wantErr        bool
	}{
		{
			name:           "successful_single_membership_retrieval",
			userID:         testUserID,
			organizationID: testOrgID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock successful query with single membership found
						if memberships, ok := dest.(*[]struct {
							MembershipID uuid.UUID `db:"membership_id"`
						}); ok {
							*memberships = []struct {
								MembershipID uuid.UUID `db:"membership_id"`
							}{
								{MembershipID: testMembershipID1},
							}
						}
						return nil
					},
				}
			},
			expected:    []uuid.UUID{testMembershipID1},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:           "successful_multiple_membership_retrieval",
			userID:         testUserID,
			organizationID: testOrgID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock successful query with multiple memberships found
						if memberships, ok := dest.(*[]struct {
							MembershipID uuid.UUID `db:"membership_id"`
						}); ok {
							*memberships = []struct {
								MembershipID uuid.UUID `db:"membership_id"`
							}{
								{MembershipID: testMembershipID1},
								{MembershipID: testMembershipID2},
							}
						}
						return nil
					},
				}
			},
			expected:    []uuid.UUID{testMembershipID1, testMembershipID2},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:           "no_memberships_found",
			userID:         testUserID,
			organizationID: testOrgID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock empty result
						if memberships, ok := dest.(*[]struct {
							MembershipID uuid.UUID `db:"membership_id"`
						}); ok {
							*memberships = []struct {
								MembershipID uuid.UUID `db:"membership_id"`
							}{}
						}
						return nil
					},
				}
			},
			expected:    nil,
			expectedErr: ErrMembershipNotFound,
			wantErr:     true,
		},
		{
			name:           "database_operation_error",
			userID:         testUserID,
			organizationID: testOrgID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock database error
						return assert.AnError
					},
				}
			},
			expected:    nil,
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up the mock database
			mockDB := tt.mockDB()

			// Execute the function under test
			result, err := GetMembershipsByUserID(mockDB, tt.userID, tt.organizationID)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result, "Should return nil on error")
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result, "Should return expected membership IDs")
			}
		})
	}
}
