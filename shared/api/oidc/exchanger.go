package oidc

import (
	"context"

	"github.com/coreos/go-oidc"
	"golang.org/x/oauth2"
)

// OAuth2TokenExchanger defines the interface for OAuth2 token exchange operations
type OAuth2TokenExchanger interface {
	Exchange(ctx context.Context, config *OIDCConfig, code string) (*oauth2.Token, error)
	VerifyIDToken(ctx context.Context, config *OIDCConfig, rawID string) (*oidc.IDToken, error)
	ExtractClaims(idToken *oidc.IDToken) (map[string]any, error)
}

// OAuth2TokenExchangerImpl implements OAuth2TokenExchanger
type OAuth2TokenExchangerImpl struct{}

// NewOAuth2TokenExchanger creates a new OAuth2 token exchanger
func NewOAuth2TokenExchanger() OAuth2TokenExchanger {
	return &OAuth2TokenExchangerImpl{}
}

// Exchange exchanges the authorization code for a token
func (e *OAuth2TokenExchangerImpl) Exchange(ctx context.Context, config *OIDCConfig, code string) (*oauth2.Token, error) {
	return config.OAuth2Config.Exchange(ctx, code)
}

// VerifyIDToken verifies and parses the ID token
func (e *OAuth2TokenExchangerImpl) VerifyIDToken(ctx context.Context, config *OIDCConfig, rawID string) (*oidc.IDToken, error) {
	return config.Verifier.Verify(ctx, rawID)
}

// ExtractClaims extracts claims from the ID token
func (e *OAuth2TokenExchangerImpl) ExtractClaims(idToken *oidc.IDToken) (map[string]any, error) {
	var claims map[string]any
	err := idToken.Claims(&claims)
	return claims, err
}
