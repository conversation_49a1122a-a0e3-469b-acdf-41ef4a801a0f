package oidc

import (
	"context"
	"testing"

	"github.com/coreos/go-oidc"
	"github.com/stretchr/testify/assert"
	"golang.org/x/oauth2"
)

func TestNewOAuth2TokenExchanger(t *testing.T) {
	t.<PERSON>llel()

	exchanger := NewOAuth2TokenExchanger()

	assert.NotNil(t, exchanger, "NewOAuth2TokenExchanger should return a non-nil exchanger")
	assert.Implements(t, (*OAuth2TokenExchanger)(nil), exchanger, "Returned exchanger should implement OAuth2TokenExchanger interface")
}

func TestOAuth2TokenExchangerImpl_Exchange(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		config      *OIDCConfig
		code        string
		ctx         context.Context
		wantErr     bool
		expectedErr error
	}{
		{
			name:        "nil_config_should_panic",
			config:      nil,
			code:        "test-code",
			ctx:         context.Background(),
			wantErr:     true,
			expectedErr: nil, // Will panic, not return error
		},
		{
			name: "empty_code_should_error",
			config: &OIDCConfig{
				OAuth2Config: &oauth2.Config{
					ClientID:     "test-client-id",
					ClientSecret: "test-client-secret",
					RedirectURL:  "http://localhost:8080/callback",
					Endpoint: oauth2.Endpoint{
						AuthURL:  "http://localhost:8080/auth",
						TokenURL: "http://localhost:8080/token",
					},
				},
			},
			code:        "",
			ctx:         context.Background(),
			wantErr:     true,
			expectedErr: nil, // OAuth2 library will return specific error
		},
		{
			name: "valid_config_network_error",
			config: &OIDCConfig{
				OAuth2Config: &oauth2.Config{
					ClientID:     "test-client-id",
					ClientSecret: "test-client-secret",
					RedirectURL:  "http://localhost:8080/callback",
					Endpoint: oauth2.Endpoint{
						AuthURL:  "http://localhost:8080/auth",
						TokenURL: "http://localhost:8080/token",
					},
				},
			},
			code:        "test-auth-code",
			ctx:         context.Background(),
			wantErr:     true,
			expectedErr: nil, // Network error expected
		},
		{
			name: "invalid_oauth2_config",
			config: &OIDCConfig{
				OAuth2Config: &oauth2.Config{
					ClientID:     "",
					ClientSecret: "",
					RedirectURL:  "",
					Endpoint: oauth2.Endpoint{
						AuthURL:  "",
						TokenURL: "",
					},
				},
			},
			code:        "test-code",
			ctx:         context.Background(),
			wantErr:     true,
			expectedErr: nil, // OAuth2 library will return specific error
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			exchanger := &OAuth2TokenExchangerImpl{}

			if tt.config == nil {
				// Test nil config panic
				assert.Panics(t, func() {
					exchanger.Exchange(tt.ctx, tt.config, tt.code)
				}, "Exchange should panic with nil config")
			} else {
				// Test with valid config
				token, err := exchanger.Exchange(tt.ctx, tt.config, tt.code)

				if tt.wantErr {
					assert.Error(t, err, "Expected error but got none")
					assert.Nil(t, token, "Token should be nil when error occurs")
				} else {
					assert.NoError(t, err, "Expected no error but got: %v", err)
					assert.NotNil(t, token, "Token should not be nil when no error")
				}
			}
		})
	}
}

func TestOAuth2TokenExchangerImpl_VerifyIDToken(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		config      *OIDCConfig
		rawID       string
		ctx         context.Context
		wantErr     bool
		expectedErr error
	}{
		{
			name:        "nil_config_should_panic",
			config:      nil,
			rawID:       "test-token",
			ctx:         context.Background(),
			wantErr:     true,
			expectedErr: nil, // Will panic, not return error
		},
		{
			name: "empty_token_should_error",
			config: &OIDCConfig{
				Verifier: createMockVerifier(),
			},
			rawID:       "",
			ctx:         context.Background(),
			wantErr:     true,
			expectedErr: nil, // OIDC library will return specific error
		},
		{
			name: "invalid_token_should_error",
			config: &OIDCConfig{
				Verifier: createMockVerifier(),
			},
			rawID:       "invalid-token",
			ctx:         context.Background(),
			wantErr:     true,
			expectedErr: nil, // OIDC library will return specific error
		},
		{
			name: "nil_verifier_should_error",
			config: &OIDCConfig{
				Verifier: nil,
			},
			rawID:       "test-token",
			ctx:         context.Background(),
			wantErr:     true,
			expectedErr: nil, // OIDC library will return specific error
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			exchanger := &OAuth2TokenExchangerImpl{}

			if tt.config == nil {
				// Test nil config panic
				assert.Panics(t, func() {
					exchanger.VerifyIDToken(tt.ctx, tt.config, tt.rawID)
				}, "VerifyIDToken should panic with nil config")
			} else {
				// Test with valid config
				idToken, err := exchanger.VerifyIDToken(tt.ctx, tt.config, tt.rawID)

				if tt.wantErr {
					assert.Error(t, err, "Expected error but got none")
					assert.Nil(t, idToken, "IDToken should be nil when error occurs")
				} else {
					assert.NoError(t, err, "Expected no error but got: %v", err)
					assert.NotNil(t, idToken, "IDToken should not be nil when no error")
				}
			}
		})
	}
}

func TestOAuth2TokenExchangerImpl_ExtractClaims(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		idToken     *oidc.IDToken
		wantErr     bool
		expectedErr error
	}{
		{
			name:        "nil_id_token_should_panic",
			idToken:     nil,
			wantErr:     true,
			expectedErr: nil, // Will panic, not return error
		},
		{
			name:        "invalid_id_token_should_error",
			idToken:     &oidc.IDToken{},
			wantErr:     true,
			expectedErr: nil, // OIDC library will return specific error
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			exchanger := &OAuth2TokenExchangerImpl{}

			if tt.idToken == nil {
				// Test nil ID token panic
				assert.Panics(t, func() {
					exchanger.ExtractClaims(tt.idToken)
				}, "ExtractClaims should panic with nil ID token")
			} else {
				// Test with valid ID token
				claims, err := exchanger.ExtractClaims(tt.idToken)

				if tt.wantErr {
					assert.Error(t, err, "Expected error but got none")
					assert.Nil(t, claims, "Claims should be nil when error occurs")
				} else {
					assert.NoError(t, err, "Expected no error but got: %v", err)
					assert.NotNil(t, claims, "Claims should not be nil when no error")
				}
			}
		})
	}
}

func TestOAuth2TokenExchangerImpl_Integration(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		oauth2Config   *oauth2.Config
		verifier       *oidc.IDTokenVerifier
		code           string
		rawIDToken     string
		ctx            context.Context
		expectExchange bool
		expectVerify   bool
		expectExtract  bool
	}{
		{
			name: "complete_flow_with_network_errors",
			oauth2Config: &oauth2.Config{
				ClientID:     "test-client-id",
				ClientSecret: "test-client-secret",
				RedirectURL:  "http://localhost:8080/callback",
				Endpoint: oauth2.Endpoint{
					AuthURL:  "http://localhost:8080/auth",
					TokenURL: "http://localhost:8080/token",
				},
			},
			verifier:       createMockVerifier(),
			code:           "test-auth-code",
			rawIDToken:     "test-raw-id-token",
			ctx:            context.Background(),
			expectExchange: false, // Will fail due to network
			expectVerify:   false, // Will fail due to invalid token
			expectExtract:  false, // Will fail due to invalid token
		},
		{
			name: "empty_parameters",
			oauth2Config: &oauth2.Config{
				ClientID:     "test-client-id",
				ClientSecret: "test-client-secret",
				RedirectURL:  "http://localhost:8080/callback",
				Endpoint: oauth2.Endpoint{
					AuthURL:  "http://localhost:8080/auth",
					TokenURL: "http://localhost:8080/token",
				},
			},
			verifier:       createMockVerifier(),
			code:           "",
			rawIDToken:     "",
			ctx:            context.Background(),
			expectExchange: false, // Will fail due to empty code
			expectVerify:   false, // Will fail due to empty token
			expectExtract:  false, // Will fail due to invalid token
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			config := &OIDCConfig{
				OAuth2Config: tt.oauth2Config,
				Verifier:     tt.verifier,
			}

			exchanger := &OAuth2TokenExchangerImpl{}

			// Step 1: Exchange authorization code for token
			token, err := exchanger.Exchange(tt.ctx, config, tt.code)
			if tt.expectExchange {
				assert.NoError(t, err, "Exchange should succeed")
				assert.NotNil(t, token, "Token should not be nil")
			} else {
				assert.Error(t, err, "Exchange should fail")
				assert.Nil(t, token, "Token should be nil when error occurs")
			}

			// Step 2: Verify ID token
			idToken, err := exchanger.VerifyIDToken(tt.ctx, config, tt.rawIDToken)
			if tt.expectVerify {
				assert.NoError(t, err, "VerifyIDToken should succeed")
				assert.NotNil(t, idToken, "IDToken should not be nil")
			} else {
				assert.Error(t, err, "VerifyIDToken should fail")
				assert.Nil(t, idToken, "IDToken should be nil when error occurs")
			}

			// Step 3: Extract claims (only if ID token is valid)
			if idToken != nil {
				claims, err := exchanger.ExtractClaims(idToken)
				if tt.expectExtract {
					assert.NoError(t, err, "ExtractClaims should succeed")
					assert.NotNil(t, claims, "Claims should not be nil")
				} else {
					assert.Error(t, err, "ExtractClaims should fail")
					assert.Nil(t, claims, "Claims should be nil when error occurs")
				}
			}
		})
	}
}

func TestOAuth2TokenExchangerImpl_MethodSignatures(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		description string
		testFn      func(t *testing.T)
	}{
		{
			name:        "exchange_method_signature",
			description: "Test Exchange method has correct signature",
			testFn: func(t *testing.T) {
				exchanger := &OAuth2TokenExchangerImpl{}
				ctx := context.Background()
				oauth2Config := &oauth2.Config{
					ClientID:     "test-client-id",
					ClientSecret: "test-client-secret",
					RedirectURL:  "http://localhost:8080/callback",
					Endpoint: oauth2.Endpoint{
						AuthURL:  "http://localhost:8080/auth",
						TokenURL: "http://localhost:8080/token",
					},
				}
				config := &OIDCConfig{
					OAuth2Config: oauth2Config,
				}
				code := "test-code"

				// This should compile without errors (will fail at runtime due to network call)
				_, _ = exchanger.Exchange(ctx, config, code)
				assert.True(t, true, "Exchange method signature is correct")
			},
		},
		{
			name:        "verify_id_token_method_signature",
			description: "Test VerifyIDToken method has correct signature",
			testFn: func(t *testing.T) {
				exchanger := &OAuth2TokenExchangerImpl{}
				ctx := context.Background()
				provider := &oidc.Provider{}
				verifier := provider.Verifier(&oidc.Config{
					ClientID: "test-client-id",
				})
				config := &OIDCConfig{
					Verifier: verifier,
				}
				rawIDToken := "test-token"

				// This should compile without errors (will fail at runtime due to invalid token)
				_, _ = exchanger.VerifyIDToken(ctx, config, rawIDToken)
				assert.True(t, true, "VerifyIDToken method signature is correct")
			},
		},
		{
			name:        "extract_claims_method_signature",
			description: "Test ExtractClaims method has correct signature",
			testFn: func(t *testing.T) {
				exchanger := &OAuth2TokenExchangerImpl{}
				idToken := &oidc.IDToken{}

				// This should compile without errors (will fail at runtime due to invalid token)
				_, _ = exchanger.ExtractClaims(idToken)
				assert.True(t, true, "ExtractClaims method signature is correct")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			tt.testFn(t)
		})
	}
}

// Helper function to create a mock verifier
func createMockVerifier() *oidc.IDTokenVerifier {
	provider := &oidc.Provider{}
	return provider.Verifier(&oidc.Config{
		ClientID: "test-client-id",
	})
}
