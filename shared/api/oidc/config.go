package oidc

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/coreos/go-oidc"
	"golang.org/x/oauth2"
	"synapse-its.com/shared/logger"
)

// Config holds the configuration for OpenID Connect (OIDC) authentication
type OIDCConfig struct {
	ClientID     string
	ClientSecret string
	RedirectURL  string
	IssuerURL    string
	Provider     *oidc.Provider
	OAuth2Config *oauth2.Config
	Verifier     *oidc.IDTokenVerifier
	Scope        string
}

// Define var injectable for testing
var (
	osGetenv        = os.Getenv
	oidcNewProvider = oidc.NewProvider
	timeSleep       = time.Sleep
)

// DefaultScopes provides the standard OIDC scopes
var DefaultScopes = []string{oidc.ScopeOpenID, "profile", "email"}

// LocalhostHTTPProxy is a custom HTTP client that rewrites requests
// to "localhost:8091" to "host.docker.internal:8091". This is necessary
// for the OIDC provider to communicate with the host machine from within
// a Docker container, as Docker containers cannot directly access services
// running on the host machine using "localhost".  This is not a security
// concern in prod, because in production, the request will simply fail
// because there is no OIDC provider listening there.
var LocalhostHTTPProxy = &http.Client{Transport: &http.Transport{
	DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
		if strings.HasSuffix(addr, "localhost:8091") {
			addr = "host.docker.internal:8091"
		}
		return (&net.Dialer{}).DialContext(ctx, network, addr)
	},
}}

// NewConfigFromService creates a new OIDC configuration from environment variables
func NewConfigFromService(ctx context.Context, serviceName string) (*OIDCConfig, error) {
	config := &OIDCConfig{
		ClientID:     os.Getenv("SYNAPSE_OIDC_CLIENT_ID"),
		ClientSecret: os.Getenv("SYNAPSE_OIDC_CLIENT_SECRET"),
		RedirectURL:  os.Getenv("SYNAPSE_OIDC_" + strings.ToUpper(serviceName) + "_CALLBACK_URL"),
		IssuerURL:    os.Getenv("SYNAPSE_OIDC_ISSUER_URL"),
	}
	return NewConfig(ctx, *config)
}

// NewConfig provides a new OIDC configuration
func NewConfig(ctx context.Context, opts OIDCConfig) (*OIDCConfig, error) {
	if opts.IssuerURL == "" {
		return nil, ErrIssuerURLRequired
	}
	if opts.ClientID == "" {
		return nil, ErrClientIDRequired
	}
	if opts.ClientSecret == "" {
		return nil, ErrClientSecretRequired
	}
	if opts.RedirectURL == "" {
		return nil, ErrRedirectURLRequired
	}

	config := &opts
	return InitializeConfig(ctx, config)
}

// InitializeConfig initializes the OIDC provider, verifier, and OAuth2 config
func InitializeConfig(ctx context.Context, config *OIDCConfig) (*OIDCConfig, error) {
	// Initialize provider with retry logic and timeout
	var err error
	start := time.Now()
	maxRetries := 10
	retryCount := 0

	for retryCount < maxRetries {
		config.Provider, err = oidcNewProvider(ctx, config.IssuerURL)
		if err != nil {
			retryCount++
			logger.Warnf("failed to init real OIDC provider (attempt %d/%d): %v", retryCount, maxRetries, err)
			if retryCount >= maxRetries {
				return nil, fmt.Errorf("failed to initialize OIDC provider after %d attempts: %w", maxRetries, err)
			}
			timeSleep(time.Second)
			continue
		}
		break
	}
	logger.Infof("OIDC provider initialization took %v", time.Since(start))

	// Initialize verifier
	config.Verifier = config.Provider.Verifier(&oidc.Config{
		ClientID: config.ClientID,
	})

	// Initialize OAuth2 config
	config.OAuth2Config = &oauth2.Config{
		ClientID:     config.ClientID,
		ClientSecret: config.ClientSecret,
		Endpoint:     config.Provider.Endpoint(),
		RedirectURL:  config.RedirectURL,
		Scopes:       DefaultScopes,
	}

	return config, nil
}

// InitializeLocalConfig creates a local development version of the OIDC config
func InitializeLocalConfig(ctx context.Context, baseConfig *OIDCConfig) (*OIDCConfig, error) {
	if baseConfig == nil {
		return nil, ErrBaseConfigRequired
	}

	// Create a copy of the base config
	localConfig := *baseConfig

	// Override URLs for local development
	localConfig.IssuerURL = strings.ReplaceAll(
		baseConfig.IssuerURL,
		"keycloak:8080",
		"localhost:8091",
	)
	// if redirect url is onramp:4200 or broker:8080, replace with localhost:4200 or localhost:8080
	redirectURL := strings.ReplaceAll(
		localConfig.RedirectURL,
		"onramp:4200",
		"localhost:4200",
	)
	redirectURL = strings.ReplaceAll(
		redirectURL,
		"broker:8080",
		"localhost:8080",
	)
	localConfig.RedirectURL = redirectURL
	// Try to initialize local provider
	provider, err := oidcNewProvider(oidc.ClientContext(ctx, LocalhostHTTPProxy), localConfig.IssuerURL)
	if err != nil {
		logger.Warnf("failed to init local OIDC provider: %v", err)
		// Return the base config if local initialization fails
		return baseConfig, nil
	}

	// Initialize local provider components
	localConfig.Provider = provider
	localConfig.Verifier = provider.Verifier(&oidc.Config{
		ClientID: localConfig.ClientID,
	})
	localConfig.OAuth2Config = &oauth2.Config{
		ClientID:     localConfig.ClientID,
		ClientSecret: localConfig.ClientSecret,
		Endpoint:     provider.Endpoint(),
		RedirectURL:  localConfig.RedirectURL,
		Scopes:       DefaultScopes,
	}

	return &localConfig, nil
}
