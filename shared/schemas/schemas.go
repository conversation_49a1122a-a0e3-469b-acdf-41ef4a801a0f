package schemas

import (
	"time"

	"cloud.google.com/go/bigquery"
)

const (
	T_EdiRawMessages               = "EdiRawMessages"
	T_RmsEngine                    = "RmsEngine"
	T_DlqMessages                  = "DlqMessages"
	T_MonitorName                  = "MonitorName"
	T_MacAddress                   = "MacAddress"
	T_RmsData                      = "RmsData"
	T_FaultNotification            = "FaultNotification"
	T_GatewayPerformanceStatistics = "GatewayPerformanceStatistics"
	T_GatewayLogMessage            = "GatewayLogMessage"
	T_FaultLogs                    = "FaultLogs"
	T_LogMonitorReset              = "LogMonitorReset"
	T_logPreviousFail              = "logPreviousFail"
	T_logACLineEvent               = "logACLineEvent"
	T_logFaultSignalSequence       = "logFaultSignalSequence"
	T_logConfiguration             = "logConfiguration"
	T_NotificationMessages         = "NotificationMessages"
	T_BatchPerformanceStats        = "BatchPerformanceStats"
	T_InviteEvents                 = "InviteEvents"
)

type EdiRawMessages struct {
	Topic                     string    `bigquery:"topic"`
	OrganizationIdentifier    string    `bigquery:"organizationidentifier"`
	SoftwareGatewayIdentifier string    `bigquery:"softwaregatewayidentifier"`
	MessageType               string    `bigquery:"messagetype"`
	MessageVersion            string    `bigquery:"messageversion"`
	PubsubID                  string    `bigquery:"pubsubid"`
	Data                      []byte    `bigquery:"data"`
	Attributes                []KV      `bigquery:"attributes"`
	PubsubTimestamp           time.Time `bigquery:"pubsubtimestamp"`
	OrderingKey               string    `bigquery:"orderingkey"`
	DeliveryAttempt           int64     `bigquery:"deliveryattempt"`
}

// HeaderRecord holds header bytes parsed as fields.
type HeaderRecord struct {
	CommVersion      string `bigquery:"commversion"`
	Model            int64  `bigquery:"model"`
	FirmwareVersion  string `bigquery:"firmwareversion"`
	FirmwareRevision string `bigquery:"firmwarerevision"`
	MonitorId        int64  `bigquery:"monitorid"`
	Volt220          bool   `bigquery:"volt220"`
	VoltDC           bool   `bigquery:"voltdc"`
	MainsDC          bool   `bigquery:"mainsdc"`
	PowerDownLevel   int64  `bigquery:"powerdownlevel"`
	BlackoutLevel    int64  `bigquery:"blackoutlevel"`
	MaxChannels      int64  `bigquery:"maxchannels"`
}

type LogMonitorResetRecord struct {
	EventTimestamp time.Time `bigquery:"eventtimestamp"`
	ResetType      string    `bigquery:"resettype"`
}

type LogMonitorReset struct {
	OrganizationIdentifier string                  `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string                  `bigquery:"softwaregatewayid"`
	TZ                     string                  `bigquery:"tz"`
	Topic                  string                  `bigquery:"topic"`
	PubsubTimestamp        time.Time               `bigquery:"pubsubtimestamp"`
	PubsubID               string                  `bigquery:"pubsubid"`
	DeviceID               string                  `bigquery:"deviceid"`
	Header                 HeaderRecord            `bigquery:"header"`
	DeviceModel            string                  `bigquery:"devicemodel"`
	Records                []LogMonitorResetRecord `bigquery:"records"`
	RawMessage             []byte                  `bigquery:"rawmessage"`
	LogUUID                string                  `bigquery:"loguuid"`
}

type LogPreviousFailRecord struct {
	DateTime                          time.Time `bigquery:"datetime"`
	Fault                             string    `bigquery:"fault"`
	ACLine                            string    `bigquery:"acline"`
	T48VDCSignalBus                   string    `bigquery:"t48vdcsignalbus"`
	RedEnable                         string    `bigquery:"redenable"`
	MCCoilEE                          string    `bigquery:"mccoilee"`
	SpecialFunction1                  string    `bigquery:"specialfunction1"`
	SpecialFunction2                  string    `bigquery:"specialfunction2"`
	WDTMonitor                        string    `bigquery:"wdtmonitor"`
	T24VDCInput                       string    `bigquery:"t24vdcinput"`
	T12VDCInput                       string    `bigquery:"t12vdcinput"`
	Temperature                       int64     `bigquery:"temperature"`
	LsFlashBit                        bool      `bigquery:"lsflashbit"`
	FaultStatus                       []bool    `bigquery:"faultstatus"`
	ChannelGreenStatus                []bool    `bigquery:"channelgreenstatus"`
	ChannelYellowStatus               []bool    `bigquery:"channelyellowstatus"`
	ChannelRedStatus                  []bool    `bigquery:"channelredstatus"`
	ChannelWalkStatus                 []bool    `bigquery:"channelwalkstatus"`
	ChannelGreenFieldCheckStatus      []bool    `bigquery:"channelgreenfieldcheckstatus"`
	ChannelYellowFieldCheckStatus     []bool    `bigquery:"channelyellowfieldcheckstatus"`
	ChannelRedFieldCheckStatus        []bool    `bigquery:"channelredfieldcheckstatus"`
	ChannelWalkFieldCheckStatus       []bool    `bigquery:"channelwalkfieldcheckstatus"`
	ChannelGreenRecurrentPulseStatus  []bool    `bigquery:"channelgreenrecurrentpulsestatus"`
	ChannelYellowRecurrentPulseStatus []bool    `bigquery:"channelyellowrecurrentpulsestatus"`
	ChannelRedRecurrentPulseStatus    []bool    `bigquery:"channelredrecurrentpulsestatus"`
	ChannelWalkRecurrentPulseStatus   []bool    `bigquery:"channelwalkrecurrentpulsestatus"`
	ChannelGreenRmsVoltage            []int64   `bigquery:"channelgreenrmsvoltage"`
	ChannelYellowRmsVoltage           []int64   `bigquery:"channelyellowrmsvoltage"`
	ChannelRedRmsVoltage              []int64   `bigquery:"channelredrmsvoltage"`
	ChannelWalkRmsVoltage             []int64   `bigquery:"channelwalkrmsvoltage"`
	HDSPSignalVoltages                []int64   `bigquery:"hdspsignalvoltages"`
	NextConflictingChannels           []bool    `bigquery:"nextconflictingchannels"`
	ChannelRedCurrentStatus           []bool    `bigquery:"channelredcurrentstatus"`
	ChannelYellowCurrentStatus        []bool    `bigquery:"channelyellowcurrentstatus"`
	ChannelGreenCurrentStatus         []bool    `bigquery:"channelgreencurrentstatus"`
	ChannelRedRmsCurrent              []int64   `bigquery:"channelredrmscurrent"`
	ChannelYellowRmsCurrent           []int64   `bigquery:"channelyellowrmscurrent"`
	ChannelGreenRmsCurrent            []int64   `bigquery:"channelgreenrmscurrent"`
}

type LogPreviousFail struct {
	OrganizationIdentifier string                  `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string                  `bigquery:"softwaregatewayid"`
	TZ                     string                  `bigquery:"tz"`
	Topic                  string                  `bigquery:"topic"`
	PubsubTimestamp        time.Time               `bigquery:"pubsubtimestamp"`
	PubsubID               string                  `bigquery:"pubsubid"`
	DeviceID               string                  `bigquery:"deviceid"`
	Header                 HeaderRecord            `bigquery:"header"`
	DeviceModel            string                  `bigquery:"devicemodel"`
	Records                []LogPreviousFailRecord `bigquery:"records"`
	RawMessage             []byte                  `bigquery:"rawmessage"`
	LogUUID                string                  `bigquery:"loguuid"`
}

type LogACLineEventRecord struct {
	EventType       string    `bigquery:"eventtype"`
	DateTime        time.Time `bigquery:"datetime"`
	LineVoltageRms  int64     `bigquery:"linevoltagerms"`
	LineFrequencyHz int64     `bigquery:"linefrequencyhz"`
}

type LogACLineEvent struct {
	OrganizationIdentifier string                 `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string                 `bigquery:"softwaregatewayid"`
	TZ                     string                 `bigquery:"tz"`
	Topic                  string                 `bigquery:"topic"`
	PubsubTimestamp        time.Time              `bigquery:"pubsubtimestamp"`
	PubsubID               string                 `bigquery:"pubsubid"`
	DeviceID               string                 `bigquery:"deviceid"`
	Header                 HeaderRecord           `bigquery:"header"`
	DeviceModel            string                 `bigquery:"devicemodel"`
	Record                 []LogACLineEventRecord `bigquery:"record"`
	VoltageType            int64                  `bigquery:"voltagetype"`
	RawMessage             []byte                 `bigquery:"rawmessage"`
	LogUUID                string                 `bigquery:"loguuid"`
}

type TraceBuffer struct {
	BufferRawBytes []byte `bigquery:"bufferrawbytes"`
	Timestamp      int64  `bigquery:"timestamp"`
	Reds           []bool `bigquery:"reds"`
	Yellows        []bool `bigquery:"yellows"`
	Greens         []bool `bigquery:"greens"`
	Walks          []bool `bigquery:"walks"`
	EE_SF_RE       bool   `bigquery:"ee_sf_re"`
	AcVoltage      int64  `bigquery:"acvoltage"`
}

type LogFaultSignalSequenceRecords struct {
	TraceRawBytes []byte        `bigquery:"tracerawbytes"`
	FaultType     string        `bigquery:"faulttype"`
	Records       []TraceBuffer `bigquery:"records"`
}

type LogFaultSignalSequence struct {
	OrganizationIdentifier string                          `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string                          `bigquery:"softwaregatewayid"`
	TZ                     string                          `bigquery:"tz"`
	Topic                  string                          `bigquery:"topic"`
	PubsubTimestamp        time.Time                       `bigquery:"pubsubtimestamp"`
	PubsubID               string                          `bigquery:"pubsubid"`
	DeviceID               string                          `bigquery:"deviceid"`
	Header                 *HeaderRecord                   `bigquery:"header"`
	DeviceModel            string                          `bigquery:"devicemodel"`
	Records                []LogFaultSignalSequenceRecords `bigquery:"records"`
	RawMessage             []byte                          `bigquery:"rawmessage"`
	LogUUID                string                          `bigquery:"loguuid"`
}

type ConfigurationChangeLogRecord struct {
	DateTime        time.Time `bigquery:"datetime"`
	Ch01Permissives []string  `bigquery:"ch01permissives"`
	Ch02Permissives []string  `bigquery:"ch02permissives"`
	Ch03Permissives []string  `bigquery:"ch03permissives"`
	Ch04Permissives []string  `bigquery:"ch04permissives"`
	Ch05Permissives []string  `bigquery:"ch05permissives"`
	Ch06Permissives []string  `bigquery:"ch06permissives"`
	Ch07Permissives []string  `bigquery:"ch07permissives"`
	Ch08Permissives []string  `bigquery:"ch08permissives"`
	Ch09Permissives []string  `bigquery:"ch09permissives"`
	Ch10Permissives []string  `bigquery:"ch10permissives"`
	Ch11Permissives []string  `bigquery:"ch11permissives"`
	Ch12Permissives []string  `bigquery:"ch12permissives"`
	Ch13Permissives []string  `bigquery:"ch13permissives"`
	Ch14Permissives []string  `bigquery:"ch14permissives"`
	Ch15Permissives []string  `bigquery:"ch15permissives"`
	Ch16Permissives []string  `bigquery:"ch16permissives"`
	Ch17Permissives []string  `bigquery:"ch17permissives"`
	Ch18Permissives []string  `bigquery:"ch18permissives"`
	Ch19Permissives []string  `bigquery:"ch19permissives"`
	Ch20Permissives []string  `bigquery:"ch20permissives"`
	Ch21Permissives []string  `bigquery:"ch21permissives"`
	Ch22Permissives []string  `bigquery:"ch22permissives"`
	Ch23Permissives []string  `bigquery:"ch23permissives"`
	Ch24Permissives []string  `bigquery:"ch24permissives"`
	Ch25Permissives []string  `bigquery:"ch25permissives"`
	Ch26Permissives []string  `bigquery:"ch26permissives"`
	Ch27Permissives []string  `bigquery:"ch27permissives"`
	Ch28Permissives []string  `bigquery:"ch28permissives"`
	Ch29Permissives []string  `bigquery:"ch29permissives"`
	Ch30Permissives []string  `bigquery:"ch30permissives"`
	Ch31Permissives []string  `bigquery:"ch31permissives"`

	RedFailEnable                   []bool `bigquery:"redfailenable"`
	GreenYellowDualEnable           []bool `bigquery:"greenyellowdualenable"`
	YellowRedDualEnable             []bool `bigquery:"yellowreddualenable"`
	GreenRedDualEnable              []bool `bigquery:"greenreddualenable"`
	MinimumYellowClearanceEnable    []bool `bigquery:"minimumyellowclearanceenable"`
	MinimumYellowRedClearanceEnable []bool `bigquery:"minimumyellowreduclearanceenable"`
	FieldCheckEnableGreen           []bool `bigquery:"fieldcheckenablegreen"`
	FieldCheckEnableYellow          []bool `bigquery:"fieldcheckenableyellow"`
	FieldCheckEnableRed             []bool `bigquery:"fieldcheckenablered"`
	YellowEnable                    []bool `bigquery:"yellowenable"`
	HdspChannelEnable               []bool `bigquery:"hdspchannelenable"`

	WalkEnableTs1             bool     `bigquery:"walkenable_ts1"`
	RedFaultTiming            string   `bigquery:"redfaulttiming"`
	RecurrentPulse            bool     `bigquery:"recurrentpulse"`
	WatchdogTiming            string   `bigquery:"watchdognngtiming"`
	WatchdogEnableSwitch      bool     `bigquery:"watchdogenableswitch"`
	ProgramCardMemory         bool     `bigquery:"programcardmemory"`
	GYEnable                  bool     `bigquery:"gyenable"`
	MinimumFlashTime          string   `bigquery:"minimumflashtime"`
	CvmLatchEnable            bool     `bigquery:"cvmlatchenable"`
	LogCvmFaults              bool     `bigquery:"logcvmfaults"`
	X24VIiInputThreshold      string   `bigquery:"x24viiinputthreshold"`
	X24VLatchEnable           bool     `bigquery:"x24vlatchenable"`
	X24VoltInhibit            bool     `bigquery:"x24voltinhibit"`
	X12vPowerSupplyMonitor    bool     `bigquery:"x12vpowersupplymonitor"`
	X48vPowerSupplyMonitor    bool     `bigquery:"x48vpowersupplymonitor"`
	Port1Disable              bool     `bigquery:"port_1disable"`
	TypeMode                  string   `bigquery:"typemode"`
	LEDGuardThresholds        bool     `bigquery:"ledguardthresholds"`
	ForceType16Mode           bool     `bigquery:"forcetype_16mode"`
	Type12WithSdlcMode        bool     `bigquery:"type_12withsdlcmode"`
	VmCvm24V3XDayLatch        bool     `bigquery:"vmcvm_24v_3xdaylatch"`
	RedFailEnabledBySSM       bool     `bigquery:"redfailenabledbyssm"`
	DualIndicationFaultTiming string   `bigquery:"dualindicationfaulttiming"`
	WDTErrorClearOnPU         bool     `bigquery:"wdterrorclearonpu"`
	MinimumFlash              bool     `bigquery:"minimumflash"`
	ConfigChangeFault         bool     `bigquery:"configchangefault"`
	RedCableFault             bool     `bigquery:"redcablefault"`
	AcLineBrownout            string   `bigquery:"aclinebrownout"`
	PinEEPolarity             string   `bigquery:"pineepolarity"`
	FlashingYellowArrows      []string `bigquery:"flashingyellowarrows"`
	FyaRedAndYellowEnable     string   `bigquery:"fyaredandyellowenable"`
	FyaRedAndGreenDisable     string   `bigquery:"fyaredandgreendisable"`
	FyaYellowTrapDetection    bool     `bigquery:"fyayellowtrapdetection"`
	FYAFlashRateFault         bool     `bigquery:"fyaflashratefault"`
	FyaFlashRateDetection     bool     `bigquery:"fyaflashratedetection"`
	Pplt5Suppression          string   `bigquery:"pplt5suppression"`
	CheckValue                string   `bigquery:"checkvalue"`
	ChangeSource              string   `bigquery:"changesource"`

	// Virtual channel settings
	RedVirtualChannel    []VirtualSetting `bigquery:"redvirtualchannel"`
	YellowVirtualChannel []VirtualSetting `bigquery:"yellowvirtualchannel"`
	GreenVirtualChannel  []VirtualSetting `bigquery:"greenvirtualchannel"`

	// Current sense data
	CurrentSenseRedEnabled      []bool `bigquery:"currentsenseredenabled"`
	CurrentSenseYellowEnabled   []bool `bigquery:"currentsenseyellowenabled"`
	CurrentSenseGreenEnabled    []bool `bigquery:"currentsensegreenenabled"`
	CurrentSenseRedThreshold    []int  `bigquery:"currentsenseredthreshold"`
	CurrentSenseYellowThreshold []int  `bigquery:"currentsenseyellowthreshold"`
	CurrentSenseGreenThreshold  []int  `bigquery:"currentsensegreenthreshold"`

	// Dark Channel Maps data
	DarkChannelX01 []bool `bigquery:"darkchannelx01"`
	DarkChannelX02 []bool `bigquery:"darkchannelx02"`
	DarkChannelX03 []bool `bigquery:"darkchannelx03"`
	DarkChannelX04 []bool `bigquery:"darkchannelx04"`
}

type LogConfiguration struct {
	OrganizationIdentifier string                         `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string                         `bigquery:"softwaregatewayid"`
	TZ                     string                         `bigquery:"tz"`
	Topic                  string                         `bigquery:"topic"`
	PubsubTimestamp        time.Time                      `bigquery:"pubsubtimestamp"`
	PubsubID               string                         `bigquery:"pubsubid"`
	DeviceID               string                         `bigquery:"deviceid"`
	Header                 *HeaderRecord                  `bigquery:"header"`
	DeviceModel            string                         `bigquery:"devicemodel"`
	Record                 []ConfigurationChangeLogRecord `bigquery:"record"`
	RawMessage             []byte                         `bigquery:"rawmessage"`
	LogUUID                string                         `bigquery:"loguuid"`
}

type VirtualSetting struct {
	Color         string `bigquery:"color"`
	Enabled       bool   `bigquery:"enabled"`
	SourceChannel int    `bigquery:"sourcechannel"`
	SourceColor   string `bigquery:"sourcecolor"`
}

type KV struct {
	Key   string `bigquery:"key"`
	Value string `bigquery:"value"`
}

type DlqMessages struct {
	Topic           string    `bigquery:"topic"`
	ID              string    `bigquery:"id"`
	Data            []byte    `bigquery:"data"`
	Attributes      []KV      `bigquery:"attributes"`
	PublishTime     time.Time `bigquery:"publishtime"`
	OrderingKey     string    `bigquery:"orderingkey"`
	DeliveryAttempt int64     `bigquery:"deliveryattempt"`
	DLQReason       string    `bigquery:"dlqreason"`
	PubsubTimestamp time.Time `bigquery:"pubsubtimestamp"`
	PubsubID        string    `bigquery:"pubsubid"`
}

type RmsEngine struct {
	OrganizationIdentifier string       `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string       `bigquery:"softwaregatewayid"`
	TZ                     string       `bigquery:"tz"`
	Topic                  string       `bigquery:"topic"`
	PubsubTimestamp        time.Time    `bigquery:"pubsubtimestamp"`
	PubsubID               string       `bigquery:"pubsubid"`
	DeviceID               string       `bigquery:"deviceid"`
	Header                 HeaderRecord `bigquery:"header"`
	RmsVersion             string       `bigquery:"rmsversion"`
	RmsRevision            string       `bigquery:"rmsrevision"`
	RawMessage             []byte       `bigquery:"rawmessage"`
}

type MonitorName struct {
	OrganizationIdentifier string       `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string       `bigquery:"softwaregatewayid"`
	TZ                     string       `bigquery:"tz"`
	Topic                  string       `bigquery:"topic"`
	PubsubTimestamp        time.Time    `bigquery:"pubsubtimestamp"`
	PubsubID               string       `bigquery:"pubsubid"`
	DeviceID               string       `bigquery:"deviceid"`
	Header                 HeaderRecord `bigquery:"header"`
	MonitorName            string       `bigquery:"monitorname"`
	RawMessage             []byte       `bigquery:"rawmessage"`
}

type MacAddress struct {
	OrganizationIdentifier string    `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string    `bigquery:"softwaregatewayid"`
	TZ                     string    `bigquery:"tz"`
	Topic                  string    `bigquery:"topic"`
	PubsubTimestamp        time.Time `bigquery:"pubsubtimestamp"`
	PubsubID               string    `bigquery:"pubsubid"`
	DeviceID               string    `bigquery:"deviceid"`
	MacAddress             string    `bigquery:"macaddress"`
}

type ChannelStatusStruct struct {
	Channel01 bigquery.NullBool `bigquery:"channel01"`
	Channel02 bigquery.NullBool `bigquery:"channel02"`
	Channel03 bigquery.NullBool `bigquery:"channel03"`
	Channel04 bigquery.NullBool `bigquery:"channel04"`
	Channel05 bigquery.NullBool `bigquery:"channel05"`
	Channel06 bigquery.NullBool `bigquery:"channel06"`
	Channel07 bigquery.NullBool `bigquery:"channel07"`
	Channel08 bigquery.NullBool `bigquery:"channel08"`
	Channel09 bigquery.NullBool `bigquery:"channel09"`
	Channel10 bigquery.NullBool `bigquery:"channel10"`
	Channel11 bigquery.NullBool `bigquery:"channel11"`
	Channel12 bigquery.NullBool `bigquery:"channel12"`
	Channel13 bigquery.NullBool `bigquery:"channel13"`
	Channel14 bigquery.NullBool `bigquery:"channel14"`
	Channel15 bigquery.NullBool `bigquery:"channel15"`
	Channel16 bigquery.NullBool `bigquery:"channel16"`
	Channel17 bigquery.NullBool `bigquery:"channel17"`
	Channel18 bigquery.NullBool `bigquery:"channel18"`
	Channel19 bigquery.NullBool `bigquery:"channel19"`
	Channel20 bigquery.NullBool `bigquery:"channel20"`
	Channel21 bigquery.NullBool `bigquery:"channel21"`
	Channel22 bigquery.NullBool `bigquery:"channel22"`
	Channel23 bigquery.NullBool `bigquery:"channel23"`
	Channel24 bigquery.NullBool `bigquery:"channel24"`
	Channel25 bigquery.NullBool `bigquery:"channel25"`
	Channel26 bigquery.NullBool `bigquery:"channel26"`
	Channel27 bigquery.NullBool `bigquery:"channel27"`
	Channel28 bigquery.NullBool `bigquery:"channel28"`
	Channel29 bigquery.NullBool `bigquery:"channel29"`
	Channel30 bigquery.NullBool `bigquery:"channel30"`
	Channel31 bigquery.NullBool `bigquery:"channel31"`
	Channel32 bigquery.NullBool `bigquery:"channel32"`
	Channel33 bigquery.NullBool `bigquery:"channel33"`
	Channel34 bigquery.NullBool `bigquery:"channel34"`
	Channel35 bigquery.NullBool `bigquery:"channel35"`
	Channel36 bigquery.NullBool `bigquery:"channel36"`
}

type ChannelVoltageStruct struct {
	Channel01 bigquery.NullInt64 `bigquery:"channel01"`
	Channel02 bigquery.NullInt64 `bigquery:"channel02"`
	Channel03 bigquery.NullInt64 `bigquery:"channel03"`
	Channel04 bigquery.NullInt64 `bigquery:"channel04"`
	Channel05 bigquery.NullInt64 `bigquery:"channel05"`
	Channel06 bigquery.NullInt64 `bigquery:"channel06"`
	Channel07 bigquery.NullInt64 `bigquery:"channel07"`
	Channel08 bigquery.NullInt64 `bigquery:"channel08"`
	Channel09 bigquery.NullInt64 `bigquery:"channel09"`
	Channel10 bigquery.NullInt64 `bigquery:"channel10"`
	Channel11 bigquery.NullInt64 `bigquery:"channel11"`
	Channel12 bigquery.NullInt64 `bigquery:"channel12"`
	Channel13 bigquery.NullInt64 `bigquery:"channel13"`
	Channel14 bigquery.NullInt64 `bigquery:"channel14"`
	Channel15 bigquery.NullInt64 `bigquery:"channel15"`
	Channel16 bigquery.NullInt64 `bigquery:"channel16"`
	Channel17 bigquery.NullInt64 `bigquery:"channel17"`
	Channel18 bigquery.NullInt64 `bigquery:"channel18"`
	Channel19 bigquery.NullInt64 `bigquery:"channel19"`
	Channel20 bigquery.NullInt64 `bigquery:"channel20"`
	Channel21 bigquery.NullInt64 `bigquery:"channel21"`
	Channel22 bigquery.NullInt64 `bigquery:"channel22"`
	Channel23 bigquery.NullInt64 `bigquery:"channel23"`
	Channel24 bigquery.NullInt64 `bigquery:"channel24"`
	Channel25 bigquery.NullInt64 `bigquery:"channel25"`
	Channel26 bigquery.NullInt64 `bigquery:"channel26"`
	Channel27 bigquery.NullInt64 `bigquery:"channel27"`
	Channel28 bigquery.NullInt64 `bigquery:"channel28"`
	Channel29 bigquery.NullInt64 `bigquery:"channel29"`
	Channel30 bigquery.NullInt64 `bigquery:"channel30"`
	Channel31 bigquery.NullInt64 `bigquery:"channel31"`
	Channel32 bigquery.NullInt64 `bigquery:"channel32"`
	Channel33 bigquery.NullInt64 `bigquery:"channel33"`
	Channel34 bigquery.NullInt64 `bigquery:"channel34"`
	Channel35 bigquery.NullInt64 `bigquery:"channel35"`
	Channel36 bigquery.NullInt64 `bigquery:"channel36"`
}

// RmsDataRow maps to the RmsData BigQuery table.
type RmsData struct {
	OrganizationIdentifier string               `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string               `bigquery:"softwaregatewayid"`
	TZ                     string               `bigquery:"tz"`
	Topic                  string               `bigquery:"topic"`
	PubsubTimestamp        time.Time            `bigquery:"pubsubtimestamp"`
	PubsubID               string               `bigquery:"pubsubid"`
	DeviceID               string               `bigquery:"deviceid"`
	Header                 HeaderRecord         `bigquery:"header"`
	IsFaulted              bool                 `bigquery:"isfaulted"`
	Fault                  string               `bigquery:"fault"`
	FaultStatus            string               `bigquery:"faultstatus"`
	MonitorTime            time.Time            `bigquery:"monitortime"`
	TemperatureF           int64                `bigquery:"temperaturef"`
	ChannelGreenStatus     ChannelStatusStruct  `bigquery:"channelgreenstatus"`
	ChannelYellowStatus    ChannelStatusStruct  `bigquery:"channelyellowstatus"`
	ChannelRedStatus       ChannelStatusStruct  `bigquery:"channelredstatus"`
	ChannelGreenVoltage    ChannelVoltageStruct `bigquery:"channelgreenvoltage"`
	ChannelYellowVoltage   ChannelVoltageStruct `bigquery:"channelyellowvoltage"`
	ChannelRedVoltage      ChannelVoltageStruct `bigquery:"channelredvoltage"`
	RawMessage             []byte               `bigquery:"rawmessage"`
}

type FaultNotification struct {
	OrganizationIdentifier string               `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string               `bigquery:"softwaregatewayid"`
	TZ                     string               `bigquery:"tz"`
	Topic                  string               `bigquery:"topic"`
	PubsubTimestamp        time.Time            `bigquery:"pubsubtimestamp"`
	PubsubID               string               `bigquery:"pubsubid"`
	DeviceID               string               `bigquery:"deviceid"`
	Header                 HeaderRecord         `bigquery:"header"`
	IsFaulted              bool                 `bigquery:"isfaulted"`
	Fault                  string               `bigquery:"fault"`
	FaultStatus            string               `bigquery:"faultstatus"`
	MonitorTime            time.Time            `bigquery:"monitortime"`
	TemperatureF           int64                `bigquery:"temperaturef"`
	ChannelGreenStatus     ChannelStatusStruct  `bigquery:"channelgreenstatus"`
	ChannelYellowStatus    ChannelStatusStruct  `bigquery:"channelyellowstatus"`
	ChannelRedStatus       ChannelStatusStruct  `bigquery:"channelredstatus"`
	ChannelGreenVoltage    ChannelVoltageStruct `bigquery:"channelgreenvoltage"`
	ChannelYellowVoltage   ChannelVoltageStruct `bigquery:"channelyellowvoltage"`
	ChannelRedVoltage      ChannelVoltageStruct `bigquery:"channelredvoltage"`
	RawMessage             []byte               `bigquery:"rawmessage"`
}

// GatewayPerformanceStatistics maps to the GatewayPerformanceStatistics BigQuery table.
type GatewayPerformanceStatistics struct {
	OrganizationIdentifier string        `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string        `bigquery:"softwaregatewayid"`
	TZ                     string        `bigquery:"tz"`
	Topic                  string        `bigquery:"topic"`
	PubsubTimestamp        time.Time     `bigquery:"pubsubtimestamp"`
	PubsubID               string        `bigquery:"pubsubid"`
	MessageTime            time.Time     `bigquery:"messagetime"`
	Statistics             []DeviceStats `bigquery:"statistics"`
	RawMessage             []byte        `bigquery:"rawmessage"`
}

// DeviceStats is one element of the top‐level `statistics` array
type DeviceStats struct {
	DeviceID  string         `bigquery:"deviceid"`
	Processes []ProcessStats `bigquery:"processes"`
}

// ProcessStats is one entry in the `processes` array
type ProcessStats struct {
	ProcessName string `bigquery:"processname"`
	Stats       Stats  `bigquery:"stats"`
}

// Stats holds the performance numbers for one process
type Stats struct {
	Count                   int64     `bigquery:"count"`
	LastExecutedTime        time.Time `bigquery:"lastexecutedtime"`
	LastExecutedElapsedTime int64     `bigquery:"lastexecutedelapsedtime"`
	TotalTime               int64     `bigquery:"totaltime"`
	MinTime                 int64     `bigquery:"mintime"`
	MaxTime                 int64     `bigquery:"maxtime"`
	ErrorCount              int64     `bigquery:"errorcount"`
}

// GatewayLogMessage maps to the GatewayLogMessage BigQuery table.
type GatewayLogMessage struct {
	OrganizationIdentifier string     `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string     `bigquery:"softwaregatewayid"`
	TZ                     string     `bigquery:"tz"`
	Topic                  string     `bigquery:"topic"`
	PubsubTimestamp        time.Time  `bigquery:"pubsubtimestamp"`
	PubsubID               string     `bigquery:"pubsubid"`
	MessageTime            time.Time  `bigquery:"messagetime"`
	LogMessage             []LogEntry `bigquery:"logentries"`
	RawMessage             []byte     `bigquery:"rawmessage"`
}

type LogEntry struct {
	Level     string            `bigquery:"level"`
	Timestamp string            `bigquery:"logtimestamp"`
	Msg       string            `bigquery:"message"`
	Extras    bigquery.NullJSON `bigquery:"extras"`
}

type RawLogMessages struct {
	LogMonitorReset        [][]byte `bigquery:"logmonitorreset"`
	LogPreviousFail        [][]byte `bigquery:"logpreviousfail"`
	LogACLineEvent         [][]byte `bigquery:"logaclineevent"`
	LogFaultSignalSequence [][]byte `bigquery:"logfaultsignalsequence"`
	LogConfiguration       [][]byte `bigquery:"logconfiguration"`
}

// FaultLogsRow maps to the FaultLogs BigQuery table.
type FaultLogs struct {
	OrganizationIdentifier string         `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string         `bigquery:"softwaregatewayid"`
	TZ                     string         `bigquery:"tz"`
	Topic                  string         `bigquery:"topic"`
	PubsubTimestamp        time.Time      `bigquery:"pubsubtimestamp"`
	PubsubID               string         `bigquery:"pubsubid"`
	DeviceID               string         `bigquery:"deviceid"`
	RawLogMessages         RawLogMessages `bigquery:"rawlogmessages"`
	LogUUID                string         `bigquery:"loguuid"`
}

type FaultLogsWithModel struct {
	OrganizationIdentifier string         `bigquery:"organizationidentifier"`
	SoftwareGatewayID      string         `bigquery:"softwaregatewayid"`
	TZ                     string         `bigquery:"tz"`
	Topic                  string         `bigquery:"topic"`
	PubsubTimestamp        time.Time      `bigquery:"pubsubtimestamp"`
	PubsubID               string         `bigquery:"pubsubid"`
	DeviceID               string         `bigquery:"deviceid"`
	RawLogMessages         RawLogMessages `bigquery:"rawlogmessages"`
	LogUUID                string         `bigquery:"loguuid"`
	DeviceModel            string         `bigquery:"devicemodel"`
	Monitorid              int64          `bigquery:"monitorid"`
	FirmwareVersion        string         `bigquery:"firmwareversion"`
	FirmwareReVision       string         `bigquery:"firmwarerevision"`
	MonitorCommVersion     string         `bigquery:"commversion"`
	Model                  int64          `bigquery:"model"`
}

type AllLogs struct {
	FaultLogs              FaultLogsWithModel     `bigquery:"fault_logs"`
	LogMonitorReset        LogMonitorReset        `bigquery:"monitor_reset"`
	LogPreviousFail        LogPreviousFail        `bigquery:"previous_fail"`
	LogConfiguration       LogConfiguration       `bigquery:"configuration_change"`
	LogACLineEvent         LogACLineEvent         `bigquery:"ac_line_event"`
	LogFaultSignalSequence LogFaultSignalSequence `bigquery:"fault_signal_sequence"`
}

type NotificationMessages struct {
	OrganizationIdentifier string            `bigquery:"organizationidentifier"`
	Topic                  string            `bigquery:"topic"`
	PubsubTimestamp        time.Time         `bigquery:"pubsubtimestamp"`
	PubsubID               string            `bigquery:"pubsubid"`
	NotificationType       string            `bigquery:"notificationtype"`
	Payload                bigquery.NullJSON `bigquery:"payload"`
	Metadata               bigquery.NullJSON `bigquery:"metadata"`
	RawMessage             []byte            `bigquery:"rawmessage"`
}

// BatchPerformanceStats tracks performance metrics for batch processing
type BatchPerformanceStats struct {
	Table           string    `bigquery:"table"`
	Timestamp       time.Time `bigquery:"timestamp"`
	BatchSize       int       `bigquery:"batch_size"`
	BatchBytes      int       `bigquery:"batch_bytes"`
	ProcessingMs    int64     `bigquery:"processing_ms"`     // Time spent on pre-processing (serialization, splitting)
	LoadingMs       int64     `bigquery:"loading_ms"`        // Time spent on BigQuery operations
	TotalDurationMs int64     `bigquery:"total_duration_ms"` // Total time (processing + loading)
	Retries         int       `bigquery:"retries"`
	Error           string    `bigquery:"error"`
	Splits          int       `bigquery:"splits"`
	DLQMessages     int       `bigquery:"dlq_messages"`
	DLQBytes        int       `bigquery:"dlq_bytes"`
	CurrentDepth    int32     `bigquery:"current_depth"` // Current queue depth when metrics were recorded
}

// InviteEvent represents the BigQuery event structure for invite audit events
type InviteEvent struct {
	UserInviteID           string              `bigquery:"userinviteid"`
	EventType              string              `bigquery:"eventtype"`
	Actor                  string              `bigquery:"actor"`
	EventTime              time.Time           `bigquery:"eventtime"`
	OrganizationIdentifier string              `bigquery:"organizationidentifier"`
	TokenHash              string              `bigquery:"tokenhash"`
	Email                  string              `bigquery:"email"`
	InviterID              string              `bigquery:"inviterid"`
	OrganizationRole       string              `bigquery:"organizationrole"`
	Status                 string              `bigquery:"status"`
	Message                bigquery.NullString `bigquery:"message"`
	RequireSSO             bool                `bigquery:"requiresso"`
	RetryCount             int64               `bigquery:"retrycount"`
	Retried                time.Time           `bigquery:"retried"`
	Expired                time.Time           `bigquery:"expired"`
	Created                time.Time           `bigquery:"created"`
	Sent                   time.Time           `bigquery:"sent"`
	Updated                time.Time           `bigquery:"updated"`
}
