{"accessCodeLifespan": 60, "accessCodeLifespanLogin": 1800, "accessCodeLifespanUserAction": 300, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "adminEventsDetailsEnabled": false, "adminEventsEnabled": false, "adminPermissionsEnabled": false, "attributes": {"cibaAuthRequestedUserHint": "login_hint", "cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaInterval": "5", "clientOfflineSessionIdleTimeout": "0", "clientOfflineSessionMaxLifespan": "0", "clientSessionIdleTimeout": "0", "clientSessionMaxLifespan": "0", "oauth2DeviceCodeLifespan": "600", "oauth2DevicePollingInterval": "5", "parRequestUriLifespan": "60", "realmReusableOtpCode": "false"}, "authenticationFlows": [{"alias": "Account verification options", "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Method with which to verity the existing account", "id": "b5390f5b-ddcb-4db5-a952-8b4a99bd5233", "providerId": "basic-flow", "topLevel": false}, {"alias": "Browser - Conditional OTP", "authenticationExecutions": [{"authenticator": "auth-otp-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the OTP is required for the authentication", "id": "aee95292-cf29-4597-8a25-ca7966de7b27", "providerId": "basic-flow", "topLevel": false}, {"alias": "Browser - Conditional Organization", "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "organization", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the organization identity-first login is to be used", "id": "05231fff-9f93-4039-8d74-9dd6c43cd09f", "providerId": "basic-flow", "topLevel": false}, {"alias": "Direct Grant - Conditional OTP", "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the OTP is required for the authentication", "id": "3ebbc48b-9ef3-4320-92d4-fdae1d2c5fff", "providerId": "basic-flow", "topLevel": false}, {"alias": "First Broker Login - Conditional Organization", "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "idp-add-organization-member", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the authenticator that adds organization members is to be used", "id": "d62f000a-cc19-4b2d-9bfa-8a0db7a7ec45", "providerId": "basic-flow", "topLevel": false}, {"alias": "First broker login - Conditional OTP", "authenticationExecutions": [{"authenticator": "auth-otp-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the OTP is required for the authentication", "id": "02ba6c60-edb9-4d8f-812b-6a90a06015b6", "providerId": "basic-flow", "topLevel": false}, {"alias": "<PERSON><PERSON> Existing Account", "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Account verification options", "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "id": "941f8250-c784-4116-aeea-6e1d50ca3d5d", "providerId": "basic-flow", "topLevel": false}, {"alias": "Reset - Conditional OTP", "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "id": "7d5329e4-6903-435b-b99a-cca6aca2f4f1", "providerId": "basic-flow", "topLevel": false}, {"alias": "User creation or linking", "authenticationExecutions": [{"authenticator": "idp-create-user-if-unique", "authenticatorConfig": "create unique user config", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Flow for the existing/non-existing user alternatives", "id": "77f805fd-d66c-46f5-bc53-1ae173d8bbeb", "providerId": "basic-flow", "topLevel": false}, {"alias": "Verify Existing Account by Re-authentication", "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "priority": 20, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "description": "Reauthentication of existing account", "id": "a42ed48d-e888-4f55-9525-23de4e526728", "providerId": "basic-flow", "topLevel": false}, {"alias": "browser", "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 25, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Organization", "priority": 26, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "forms", "priority": 30, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Browser based authentication", "id": "6056cc1b-a137-4c39-ad4c-d50b7706b7c1", "providerId": "basic-flow", "topLevel": true}, {"alias": "clients", "authenticationExecutions": [{"authenticator": "client-jwt", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "client-secret", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 30, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 40, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Base authentication for clients", "id": "06e6340b-0242-4892-b3f6-865cf7c279e6", "providerId": "client-flow", "topLevel": true}, {"alias": "direct grant", "authenticationExecutions": [{"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "priority": 30, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "description": "OpenID Connect Resource Owner Grant", "id": "0e795d60-87a0-40d8-a806-d8108f4c753b", "providerId": "basic-flow", "topLevel": true}, {"alias": "docker auth", "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Used by Docker clients to authenticate against the IDP", "id": "0e4f8cb9-363d-4e99-a997-0a0040167a08", "providerId": "basic-flow", "topLevel": true}, {"alias": "first broker login", "authenticationExecutions": [{"authenticator": "idp-review-profile", "authenticatorConfig": "review profile config", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "First Broker Login - Conditional Organization", "priority": 50, "requirement": "CONDITIONAL", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "User creation or linking", "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "id": "780ad402-6f86-4d60-916d-79bf41cdfde5", "providerId": "basic-flow", "topLevel": true}, {"alias": "forms", "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "priority": 20, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "description": "Username, password, otp and other auth forms.", "id": "32ce587b-d8a5-4916-b0f6-f7a9ad5ae79f", "providerId": "basic-flow", "topLevel": false}, {"alias": "registration", "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "registration form", "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Registration flow", "id": "d79e76f9-c859-4abc-8120-d1920094498d", "providerId": "basic-flow", "topLevel": true}, {"alias": "registration form", "authenticationExecutions": [{"authenticator": "registration-password-action", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 50, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 60, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "registration-terms-and-conditions", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 70, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "registration-user-creation", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Registration form", "id": "e115ced8-7386-4aa1-b043-0e3138a62322", "providerId": "form-flow", "topLevel": false}, {"alias": "reset credentials", "authenticationExecutions": [{"authenticator": "reset-credential-email", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 30, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "priority": 40, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "description": "Reset credentials for a user if they forgot their password or something", "id": "71bcce26-63e1-4957-a974-a69bec367724", "providerId": "basic-flow", "topLevel": true}, {"alias": "saml ecp", "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "SAML ECP Profile Authentication Flow", "id": "99dc64af-003c-4b09-bbe9-6b7d7b173827", "providerId": "basic-flow", "topLevel": true}, {"alias": "Organization", "authenticationExecutions": [{"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Browser - Conditional Organization", "priority": 10, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "id": "462ddb13-cbf7-40c8-a01a-2e7c22e670fe", "providerId": "basic-flow", "topLevel": false}], "authenticatorConfig": [{"alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}, "id": "3e28ebb6-6005-4e2d-a393-ad4abafe8718"}, {"alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}, "id": "dd21dee6-46f7-47ac-bb96-6878bc4a5310"}], "browserFlow": "browser", "browserSecurityHeaders": {"contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "contentSecurityPolicyReportOnly": "", "referrerPolicy": "no-referrer", "strictTransportSecurity": "max-age=********; includeSubDomains", "xContentTypeOptions": "nosniff", "xFrameOptions": "SAMEORIGIN", "xRobotsTag": "none"}, "bruteForceProtected": false, "bruteForceStrategy": "MULTIPLE", "clientAuthenticationFlow": "clients", "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "clientPolicies": {"policies": []}, "clientProfiles": {"profiles": []}, "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clientScopes": [{"attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}, "description": "OpenID Connect built-in scope: offline_access", "id": "520d3541-48ff-4a24-90c1-30dad7b11717", "name": "offline_access", "protocol": "openid-connect"}, {"attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "description": "SAML role list", "id": "992baee2-4c36-4875-ae43-3705d6d3582a", "name": "role_list", "protocol": "saml", "protocolMappers": [{"config": {"attribute.name": "Role", "attribute.nameformat": "Basic", "single": "false"}, "consentRequired": false, "id": "********-9aab-43c6-b47e-cc66a6606de9", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper"}]}, {"attributes": {"consent.screen.text": "", "display.on.consent.screen": "false", "include.in.token.scope": "false"}, "description": "OpenID Connect scope for add allowed web origins to the access token", "id": "4e44aeac-eda0-4ab7-b189-d2f09f96a2b3", "name": "web-origins", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "introspection.token.claim": "true"}, "consentRequired": false, "id": "4451b208-f38f-4399-ae83-3f033f2b12d2", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper"}]}, {"attributes": {"consent.screen.text": "${addressScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "OpenID Connect built-in scope: address", "id": "aaa03f32-c174-4d48-8a87-08b9065ad8ab", "name": "address", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "id.token.claim": "true", "introspection.token.claim": "true", "user.attribute.country": "country", "user.attribute.formatted": "formatted", "user.attribute.locality": "locality", "user.attribute.postal_code": "postal_code", "user.attribute.region": "region", "user.attribute.street": "street", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "2064dd9a-9815-4c6b-b14b-2f87144999f3", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper"}]}, {"attributes": {"consent.screen.text": "${emailScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "OpenID Connect built-in scope: email", "id": "283e26f7-596e-4b53-b90f-70fc333ba147", "name": "email", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "email", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "email", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "86f38969-4c74-4645-bdaf-731ebe7000b2", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "email_verified", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "boolean", "user.attribute": "emailVerified", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "0ed1b3d1-6a58-4725-8541-2642ad4643a3", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper"}]}, {"attributes": {"consent.screen.text": "${organizationScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "Additional claims about the organization a subject belongs to", "id": "9dece66f-9334-4aa5-839d-05715c913ecd", "name": "organization", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "organization", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "multivalued": "true", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "14fc1736-8778-4d00-96a7-29887aa03701", "name": "organization", "protocol": "openid-connect", "protocolMapper": "oidc-organization-membership-mapper"}]}, {"attributes": {"consent.screen.text": "${phoneScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "OpenID Connect built-in scope: phone", "id": "ca016fa1-10e8-460c-9ec0-6e31db54e13d", "name": "phone", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "phone_number", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "phoneNumber", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "8ff9b802-7728-48f6-9e4e-508abbce5237", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "phone_number_verified", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "boolean", "user.attribute": "phoneNumberVerified", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "398c6cff-1638-4ac4-b7fa-8a49e3fbb377", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}]}, {"attributes": {"consent.screen.text": "${profileScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "OpenID Connect built-in scope: profile", "id": "e61549fc-4f01-41e6-8147-bb3ca1fdf5f2", "name": "profile", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "birthdate", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "birthdate", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "fdd5173a-b202-445d-ac58-95ddaecc9997", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "family_name", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "lastName", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "52c30e8f-5871-4569-b7b9-6bb869f14675", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "gender", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "gender", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "213c8ac8-8398-424e-a8ff-cb971baf096d", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "given_name", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "firstName", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "11ce6bc2-aa6e-47cb-94aa-67db0fae133e", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "locale", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "locale", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "fdeaa21f-09f4-4033-bcd2-4cc17e76bffb", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "middle_name", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "middleName", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "426dfb13-9fff-41e4-ae86-56eb83d351bd", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "nickname", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "nickname", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "d1ab47cf-8623-487c-8416-d5d8bcc97bb0", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "picture", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "picture", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "43e25483-0e71-4b6b-8bf1-0f47893f6cb1", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "preferred_username", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "username", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "90f410d8-ec6c-49d7-9d45-5afe41e87c6a", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "profile", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "profile", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "a70f6dfb-b10e-4920-9772-8ada10109264", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "updated_at", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "long", "user.attribute": "updatedAt", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "f3044212-578c-4c49-8cca-2e9c74226025", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "website", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "website", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "8ea9ffbb-0c3f-498f-83ac-c8334cfbaab2", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "zoneinfo", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "zoneinfo", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "547640bd-ccad-4702-bb29-d4e543d4a0ab", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "id.token.claim": "true", "introspection.token.claim": "true", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "1acb8cf7-d813-45b8-bd84-88376f13897d", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper"}]}, {"attributes": {"consent.screen.text": "${rolesScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "false"}, "description": "OpenID Connect scope for add user roles to the access token", "id": "a7a4a5fa-6353-43d7-81b7-bf6ced768d21", "name": "roles", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "realm_access.roles", "introspection.token.claim": "true", "jsonType.label": "String", "multivalued": "true", "user.attribute": "foo"}, "consentRequired": false, "id": "1d3429fd-72bb-406a-bb5d-c78441362f9e", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "introspection.token.claim": "true", "jsonType.label": "String", "multivalued": "true", "user.attribute": "foo"}, "consentRequired": false, "id": "5d39aefb-7329-418a-a426-85b2e63fcbab", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper"}, {"config": {"access.token.claim": "true", "introspection.token.claim": "true"}, "consentRequired": false, "id": "a896d0ca-ec07-47bc-a3f4-98338e5b87c1", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper"}]}, {"attributes": {"display.on.consent.screen": "false"}, "description": "Organization Membership", "id": "3f4de095-488f-45c9-8f9f-86079c11ff84", "name": "saml_organization", "protocol": "saml", "protocolMappers": [{"config": {}, "consentRequired": false, "id": "b460d629-e146-411d-9eab-58d13ac2cc4d", "name": "organization", "protocol": "saml", "protocolMapper": "saml-organization-membership-mapper"}]}, {"attributes": {"display.on.consent.screen": "false", "include.in.token.scope": "false"}, "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "id": "9c8da029-eaa3-4fc0-bd97-ab0ea5445d96", "name": "acr", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "id.token.claim": "true", "introspection.token.claim": "true", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "87f334fe-53c3-4981-bafc-6afe683bd836", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper"}]}, {"attributes": {"display.on.consent.screen": "false", "include.in.token.scope": "false"}, "description": "OpenID Connect scope for add all basic claims to the token", "id": "9565c3a0-00e7-48c2-9386-d4720d0efef5", "name": "basic", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "auth_time", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "long", "user.session.note": "AUTH_TIME", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "439613e3-269e-419b-a7f7-6040e10c5d9c", "name": "auth_time", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper"}, {"config": {"access.token.claim": "true", "introspection.token.claim": "true"}, "consentRequired": false, "id": "32837e9b-157a-46ee-b518-630f0b6285b2", "name": "sub", "protocol": "openid-connect", "protocolMapper": "oidc-sub-mapper"}]}, {"attributes": {"display.on.consent.screen": "false", "include.in.token.scope": "false"}, "description": "Specific scope for a client enabled for service accounts", "id": "********-4d34-494e-b798-6b1ee1080681", "name": "service_account", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "clientAddress", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.session.note": "clientAddress", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "84bdb502-d094-4627-ac69-3fe32ab2513a", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "clientHost", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.session.note": "clientHost", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "4b12cc08-3c67-408a-8668-626bd36ad3f5", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "client_id", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.session.note": "client_id", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "d3ab6f1f-8ee1-4f05-b624-c8735b17<PERSON>e", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper"}]}, {"attributes": {"display.on.consent.screen": "false", "include.in.token.scope": "true"}, "description": "Microprofile - JWT built-in scope", "id": "c7b19c9c-c62e-47d6-96bd-f2fdb407e834", "name": "microprofile-jwt", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "groups", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "multivalued": "true", "user.attribute": "foo", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "6683a8e0-9648-4f71-bb32-755699834f96", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "upn", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "username", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "0e48cd40-b859-4066-9b9b-e37b9bdf20c0", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}]}], "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clients": [{"adminUrl": "", "alwaysDisplayInConsole": true, "attributes": {"backchannel.logout.revoke.offline.tokens": "false", "backchannel.logout.session.required": "true", "client.secret.creation.time": "1748451611", "display.on.consent.screen": "false", "frontchannel.logout.session.required": "true", "oauth2.device.authorization.grant.enabled": "false", "oidc.ciba.grant.enabled": "false", "post.logout.redirect.uris": "+", "realm_client": "false", "standard.token.exchange.enabled": "false"}, "authenticationFlowBindingOverrides": {}, "baseUrl": "", "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "onramp-dev-client", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "description": "", "directAccessGrantsEnabled": true, "enabled": true, "frontchannelLogout": true, "fullScopeAllowed": true, "id": "f2b59a22-bdd4-4507-a9a3-e065b0a81316", "implicitFlowEnabled": false, "name": "Onramp Dev Client", "nodeReRegistrationTimeout": -1, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": false, "redirectUris": ["http://broker:8080/*", "http://localhost:4200/*", "http://localhost:8080/*", "http://onramp:4200/*"], "rootUrl": "", "secret": "eMb2r6XVG1cBUVhUCE0b9GeP5HgWUBb6", "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": ["*"]}, {"alwaysDisplayInConsole": false, "attributes": {"client.use.lightweight.access.token.enabled": "true", "pkce.code.challenge.method": "S256", "post.logout.redirect.uris": "+", "realm_client": "false"}, "authenticationFlowBindingOverrides": {}, "baseUrl": "/admin/onramp-dev/console/", "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "security-admin-console", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": true, "id": "357b838a-90b4-4e60-b4c5-e0d46acc36a4", "implicitFlowEnabled": false, "name": "${client_security-admin-console}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "locale", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "locale", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "69a127a4-f1b0-4d01-ae0c-d719dda40b78", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}], "publicClient": true, "redirectUris": ["/admin/onramp-dev/console/*"], "rootUrl": "${authAdminUrl}", "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": ["+"]}, {"alwaysDisplayInConsole": false, "attributes": {"pkce.code.challenge.method": "S256", "post.logout.redirect.uris": "+", "realm_client": "false"}, "authenticationFlowBindingOverrides": {}, "baseUrl": "/realms/onramp-dev/account/", "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "account-console", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": false, "id": "4f90560b-df33-476d-8d40-92003d262556", "implicitFlowEnabled": false, "name": "${client_account-console}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "protocolMappers": [{"config": {}, "consentRequired": false, "id": "2e43a263-08b4-4051-ac8d-3c2654b5cac8", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper"}], "publicClient": true, "redirectUris": ["/realms/onramp-dev/account/*"], "rootUrl": "${authBaseUrl}", "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": []}, {"alwaysDisplayInConsole": false, "attributes": {"post.logout.redirect.uris": "+", "realm_client": "false"}, "authenticationFlowBindingOverrides": {}, "baseUrl": "/realms/onramp-dev/account/", "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "account", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": false, "id": "b41743a6-ae46-44a0-9058-7f36791c093b", "implicitFlowEnabled": false, "name": "${client_account}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": true, "redirectUris": ["/realms/onramp-dev/account/*"], "rootUrl": "${authBaseUrl}", "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": []}, {"alwaysDisplayInConsole": false, "attributes": {"client.use.lightweight.access.token.enabled": "true", "post.logout.redirect.uris": "+", "realm_client": "false"}, "authenticationFlowBindingOverrides": {}, "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "admin-cli", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": true, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": true, "id": "adbf0785-2c13-4cae-9e36-3f2018e264f9", "implicitFlowEnabled": false, "name": "${client_admin-cli}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": true, "redirectUris": [], "serviceAccountsEnabled": false, "standardFlowEnabled": false, "surrogateAuthRequired": false, "webOrigins": []}, {"alwaysDisplayInConsole": false, "attributes": {"post.logout.redirect.uris": "+", "realm_client": "true"}, "authenticationFlowBindingOverrides": {}, "bearerOnly": true, "clientAuthenticatorType": "client-secret", "clientId": "broker", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": false, "id": "afdd58cc-6874-4937-b1c2-7ec8b0b8d699", "implicitFlowEnabled": false, "name": "${client_broker}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": false, "redirectUris": [], "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": []}, {"alwaysDisplayInConsole": false, "attributes": {"post.logout.redirect.uris": "+", "realm_client": "true"}, "authenticationFlowBindingOverrides": {}, "bearerOnly": true, "clientAuthenticatorType": "client-secret", "clientId": "realm-management", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": false, "id": "4de350ec-447e-47ef-8994-c66739909ac5", "implicitFlowEnabled": false, "name": "${client_realm-management}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": false, "redirectUris": [], "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": []}], "components": {"org.keycloak.keys.KeyProvider": [{"config": {"algorithm": ["RSA-OAEP"], "certificate": ["MIICozCCAYsCBgGW7//xUjANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApvbnJhbXAtZGV2MB4XDTI1MDUyMDIzMTkxMFoXDTM1MDUyMDIzMjA1MFowFTETMBEGA1UEAwwKb25yYW1wLWRldjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKhgtWyPRyB9zV1wfH1s+SKK+WuyJnRuI9DuuwhKP3Wd1zLJF/+YO4uB/3MUyWLGEulpMvyL01f0vtG66Z6sDFP9kljlvTh0YFHRY9FdczX8+3RU1jbuoXXsRAITtwlysjptt33WKwM3FYUOs50V4rDvO/QLCMnL1p0Eg2oYWV+SkLSityYlD7NlmbBzAFzM2W0ORH0MITbndH7IW6SJsXfdSWxdwoT1FmanEpEOl92w5rDkn8YPF+kzTHvNDmcLvKB2Y5WC06KM6iV9k3DI4tfaYKT5ePAXhtNn5nhZnZNzmv7NXFLF0b1gjroFvQquCf6aFaCN3G1KaT3Cnd+tvX8CAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAULiEwKYPQ3qrI6K/k9OJltTMxyJlvV2g6LhblCxAPvqi6XfYylneYsu6NqKeoYv/Nw4vo5ZII5vgQi9c062fUkGfVd4ZEPjDE3FEp41uYwLLyjcs2X/ElNp0vVEHxDpn/NGbAiy4indb9tVDOKg8mmtQSArwuPqOFRPfH+zfWHUzI3kgP3LzoUy+4QJvfeworb9X5kJ9zsIVV9IlZfgiNxC63aQrwbnaFgerVH9tln8iJ0jBURtB6Eh+qFwB5prcaVF8L+Lo7FT8JLLr83ciz341faNRUKyV4fXUXxl1cY2g24J2V/XAfSfwL/+evTQwkWTHP3C0KMtbw0R0I7dZbA=="], "keyUse": ["ENC"], "priority": ["100"], "privateKey": ["MIIEogIBAAKCAQEAqGC1bI9HIH3NXXB8fWz5Ior5a7ImdG4j0O67CEo/dZ3XMskX/5g7i4H/cxTJYsYS6Wky/IvTV/S+0brpnqwMU/2SWOW9OHRgUdFj0V1zNfz7dFTWNu6hdexEAhO3CXKyOm23fdYrAzcVhQ6znRXisO879AsIycvWnQSDahhZX5KQtKK3JiUPs2WZsHMAXMzZbQ5EfQwhNud0fshbpImxd91JbF3ChPUWZqcSkQ6X3bDmsOSfxg8X6TNMe80OZwu8oHZjlYLToozqJX2TcMji19pgpPl48BeG02fmeFmdk3Oa/s1cUsXRvWCOugW9Cq4J/poVoI3cbUppPcKd3629fwIDAQABAoIBAAHnZ2BwmTPLy+zZnSiH6t0LEutFAAqmcGY9WiidczPqbyjmWf5lRwBscw3Yk7cNCxnQAm9OSh7ixIjweErM98p/HkB2+QRF57fOA2HEgrDwwf2Ve1SdkrdCXQ8rZcoPK+bdkUrfQCVe/pVa+SWEvjkxZuKl1fXhvpFXLlXlukD8VKDUjEP2g8DEfuJ9Hki8PR4C43MAjQLmkljO0zxZXwBi725ZLkOgoxO4cdyapFcyckHJyQZr+4U7aJR3nYv4hOjRxkD89cCb0po39Wg4hql++y23SDiRX6ia7H4DANZ+gvvE4mu59Boti7SFqUN4lheOWmDueAkO1FviYbcSDUkCgYEA4Wb6W6xGxjmH8L9npffFOwBOCUQDWbBHUC6Ihx8AhhGffl6DFtGg7WWgmYicKgioO2aRTD42+sh93M310ahN8Q2VQC5tuKzu4tAszwqohA1onHEhYfeHeBa2SnmpKGnlqdyexACnnnQB+es1AgGcv11gqcw6qdexr42wfoXvcUcCgYEAvzwOMc6Kuld78KXPr/bNmGOTQmF4s5oxFs497y0tlIA60uu2L7/CxNVbNvocAhFogoXJUx0Ytttpjx6tzBj6VEtsGa17JFviR1fO/KVUP3ZL1zEh53cjfOLqJzwa0DCrRlgjNMq+ZXJySxZ3XuNZkL74sMOfiPUkUTDrIPDYLgkCgYA4Iu+DWVqfD8D9go4L1bN3SFInGJtFARQhshUqodv9h/ITBPLdAgR0W3UuB8ns5g8QDDFvrab/umMuVNU+DA6tVFaxj16gOeGc93/RaLQjfztoivxna8B/dhicX8RzXjxmm52m7wrP+dBsL9oZz9D8EAN9BnItzpxgPoeQW0cRDQKBgAZZeB8vPGvze6oB12OATlNA+yK5Gcjc1cRsFKJcXacQmuNl8Calt1HRw7WPmF55NX8f8g9QaAGqR1b5LcwZCDbESlKP8AbFBj5YLAc3dhSlWN9MQlPtIMCEyEGFPV6sPwOW6ZRXJvs3UZQsqsn7wn2L2bYEyMdDXHFVrnjphkQBAoGAeJywhaOa/20vSULwEugzEVgNgqyLhcTpUaRX8q1Vx7eb1ZWaogr3l10sDIKsP8H3oBNWMAoD/KbR0/NGUh3ZKoGmJukFyth3bVE1SZNF/PsAuAWWgWfi5c/wqpWmWRxFNPbq5df9ypDAaPKF+OFrdcu6+mH4YOVe0rSklsfOot0="]}, "id": "d8b989c7-aa76-4ca3-a1aa-9885ca0a57cc", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}}, {"config": {"algorithm": ["HS512"], "kid": ["ebb3d571-6685-4452-9691-9e9ac3c059d2"], "priority": ["100"], "secret": ["45TWTtC6e3YJFJdd5FgV2wFD-HXuFSAusJxmvsO6-pwjLui6iBxHhh0QHjfw_SvosBfFbuVHylWp0QDfZhWSXxiaqjtWl9pAvOiFbdQkfg-0IjaYBuZ2gvm8FR0KE-sYdqYBUXZZw-hBL8_H5kuAdceq3sShaO76kwsnAjbC-fI"]}, "id": "fadf53cf-1867-42a5-bf1c-93a85236acc6", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}}, {"config": {"certificate": ["MIICozCCAYsCBgGW7//waDANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApvbnJhbXAtZGV2MB4XDTI1MDUyMDIzMTkxMFoXDTM1MDUyMDIzMjA1MFowFTETMBEGA1UEAwwKb25yYW1wLWRldjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBANRPDTAXfZ3FQa3sEFXSTXEkU2wqUN7bxTkgMqBULMkK9uWj9S1X1MN+dX5xuI18/GvPhZvYrh4x+8WWzvsp1PyCC6cg9h7bB+Eo4NNnoSHrv1Pgy+Xbzao37EDJk3VAqlz8RLeXCg+2cFn7H8qd3pby+mSP7okNGUCbG8yudlk0+ygSglNn/kupFJR+20fdzb0JwdeR20P8yC9n8WkDMuD6z+tWPXD6p11FXlV8PRFEu0UeUsPQBDW46rEfu3l9b/VeAYqbJ+FPOIGHNLMVot2NgKMibD0lTi8W+9UPRYg8GxWawefbKh806tHCJUAPcafNY//TR4yN279a0xiTaOUCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEANwHYPWZSwSNoHyaBZg8+6xBq2V5VljjAepH+UETujUY5icSXTzSs2UY1ej/Fpn0DGBPh6A2802S1t3vmesPjf3L6aUE6Ic1fuGYkAUYhQ0MTUb8C6UoT517v/F/Qbq3v5bqoa+E86acc2ae0uVuqPiZqRvd/34i6W55xd//4dMF4iZLyqGrheCLwNtB5HsLwp8MpmMYq6kQ75EKeoa76nykGmMyns9R2X/1p7x/NCAcT+S+NtdCAfZBIYhnnYoG2Bf8f9aC+p0vB9JYczwCghzZutArmy/qTG8mTPvkHhOoksfksVSyGO4Ja7bQnxCPDmyz2Ubec8XzoE1Rns4d16A=="], "keyUse": ["SIG"], "priority": ["100"], "privateKey": ["MIIEpAIBAAKCAQEA1E8NMBd9ncVBrewQVdJNcSRTbCpQ3tvFOSAyoFQsyQr25aP1LVfUw351fnG4jXz8a8+Fm9iuHjH7xZbO+ynU/IILpyD2HtsH4Sjg02ehIeu/U+DL5dvNqjfsQMmTdUCqXPxEt5cKD7ZwWfsfyp3elvL6ZI/uiQ0ZQJsbzK52WTT7KBKCU2f+S6kUlH7bR93NvQnB15HbQ/zIL2fxaQMy4PrP61Y9cPqnXUVeVXw9EUS7RR5Sw9AENbjqsR+7eX1v9V4Bipsn4U84gYc0sxWi3Y2AoyJsPSVOLxb71Q9FiDwbFZrB59sqHzTq0cIlQA9xp81j/9NHjI3bv1rTGJNo5QIDAQABAoIBABSBYRbywd9t2JrdeLNtSS0DEuqYyLBJnioXDcS19JBjVnbWB91eBQ7vpRX2ElraBkA8bh1gB2sjrTtHhky0XN2ZitoUwSi4Q3HQAqzwbpvp2tQVW2QjP2EwHwmf3nXd7cZm2BU6SY68cmwCaO7nIgNGyv/Q9fs3MGfwwzXY6NXTLs7W5mcwbDPDBIFICDO7mer3403h14z1QgLnPo67W8rCYfTn3FnKTuks9x6T7v/680I7JNnIuY6Cnokryo1YuMJCjyN+XznDZrY8GOI8vYyxQ9JU4YUFTfrnTkjNXGS6AQlT/WxQmNA1d9ACGyOhquFMIBMERPeNIPriOEJ1IBkCgYEA9qFpUVUhqFz/uFY+ZDc+5Oy442or5hwU9s5UJv4WFhLP2PScoNG3uFMWMkQuUb3upcFkZhnR+26TlVEtdNPzZ/lRi0dNaiB1Dv7TnTuVzsit6TgLo+3h1aAn2XUmEYuuLoLONA+PIPj2oTVnbZMVoGUJlqcutHoYTdkpn4c0Yo0CgYEA3F/Ytps24WAg1L5v95hbyWA5KF4/ALNalsJim35aUf6VD4WUlcqFGhz8T+OZv5vvZbe4i8sp1Nag9WjpZkxuHEnE4Vs3Oa2dsHoA8i3bI6XICtyTm8uBG4I2imHJJLH4RVrLEVYGutkBFbt1hAd/SC1wyTmwec1JPJDgs7zVNbkCgYEAu52Yn52SaCCvC8zX06bPOpj2IVIAKSHNtN7K14rMxGf1I4cZq6E+EINsTDrfvaEt7XxL4vvbaFj6/kjtuoUOEldwd/J66rPcONcTpfXRBahV3Cede0wYU0ltZxieKBTJIWqycJoxeJTMEWCt2IZemlCiVa/CcNq8qGP+LPROnnUCgYEA29+Vbbw3aW5r01eehz6wdZ9X5/sAiUZpn/maQOI0Y5LKTo5MzZcebgt1go/nmtGObcJ8kp3FwuHR/okKNlMTmY3QrbhAxX9/0dAomXk/J7YEfTHO5KaKzlOA71jZQ4990lTgPw/B7zMMbg53FaAVUoJkAaFZfm1gINm8GBjT8aECgYBgPHqYDNEhOOr+alqnq+4tCwHOmyxa/Q5Gq9+VkHZdsZkLtnvSJh9pn849Xg2TI7nbWJJdKCFepu7U6giG+ja3N13B9UgTH8nbqaDSsWtlcjKo3umK5IobRWPnMP2qvXz2RtZ67+mYsEo+E7ABE2h3bEbblzm+0B1N8aho3bPvRw=="]}, "id": "628b91a5-f005-4667-a8af-3dd1f1e6766b", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}}, {"config": {"kid": ["cf8fe5bc-a86a-49e2-b64e-e9fa3717d043"], "priority": ["100"], "secret": ["yb9NPuvrXsYW_qWrAVIy6A"]}, "id": "5e416dc0-6ff2-4901-aee0-5de571784268", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}}], "org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"config": {}, "id": "5cae7dae-bb15-4b3f-8b06-74452af3c6ec", "name": "Consent Required", "providerId": "consent-required", "subComponents": {}, "subType": "anonymous"}, {"config": {}, "id": "b74e96bb-2ef7-4257-b5a0-539671fd6057", "name": "Full Scope Disabled", "providerId": "scope", "subComponents": {}, "subType": "anonymous"}, {"config": {"allow-default-scopes": ["true"]}, "id": "69ff86c0-6f03-4135-9738-b7994d1ca57f", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subComponents": {}, "subType": "authenticated"}, {"config": {"allow-default-scopes": ["true"]}, "id": "b665538e-2e3b-4ea2-94e4-1c6e480d254b", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subComponents": {}, "subType": "anonymous"}, {"config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper"]}, "id": "3d3a10a0-f7c8-48c7-bd86-ab7abcc93168", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subComponents": {}, "subType": "authenticated"}, {"config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper"]}, "id": "b25f7bdb-fde8-40e3-814e-4500902a752d", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subComponents": {}, "subType": "anonymous"}, {"config": {"client-uris-must-match": ["true"], "host-sending-registration-request-must-match": ["true"]}, "id": "0fa68043-7a62-442f-9b98-27b27c733b01", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subComponents": {}, "subType": "anonymous"}, {"config": {"max-clients": ["200"]}, "id": "5e91f032-96bc-4b57-a18c-5894baa1d0f0", "name": "Max Clients Limit", "providerId": "max-clients", "subComponents": {}, "subType": "anonymous"}]}, "defaultDefaultClientScopes": ["acr", "basic", "email", "profile", "role_list", "roles", "saml_organization", "web-origins"], "defaultOptionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "defaultRole": {"clientRole": false, "composite": true, "containerId": "3c94ed80-7a88-42fb-8991-2ab7ac590e56", "description": "${role_default-roles}", "id": "d7f25f24-2f23-41b5-8abb-6c52b5b3e2db", "name": "default-roles-onramp-dev"}, "defaultSignatureAlgorithm": "RS256", "directGrantFlow": "direct grant", "dockerAuthenticationFlow": "docker auth", "duplicateEmailsAllowed": false, "editUsernameAllowed": false, "enabled": true, "enabledEventTypes": [], "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "failureFactor": 30, "firstBrokerLoginFlow": "first broker login", "groups": [], "id": "3c94ed80-7a88-42fb-8991-2ab7ac590e56", "identityProviderMappers": [], "identityProviders": [], "internationalizationEnabled": false, "keycloakVersion": "26.3.3", "localizationTexts": {}, "loginWithEmailAllowed": true, "maxDeltaTimeSeconds": 43200, "maxFailureWaitSeconds": 900, "maxTemporaryLockouts": 0, "minimumQuickLoginWaitSeconds": 60, "notBefore": 0, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespan": 5184000, "offlineSessionMaxLifespanEnabled": false, "organizationsEnabled": false, "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyCodeReusable": false, "otpPolicyDigits": 6, "otpPolicyInitialCounter": 0, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyType": "totp", "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "permanentLockout": false, "quickLoginCheckMilliSeconds": 1000, "realm": "onramp-dev", "refreshTokenMaxReuse": 0, "registrationAllowed": false, "registrationEmailAsUsername": false, "registrationFlow": "registration", "rememberMe": false, "requiredActions": [{"alias": "CONFIGURE_TOTP", "config": {}, "defaultAction": false, "enabled": true, "name": "Configure OTP", "priority": 10, "providerId": "CONFIGURE_TOTP"}, {"alias": "TERMS_AND_CONDITIONS", "config": {}, "defaultAction": false, "enabled": false, "name": "Terms and Conditions", "priority": 20, "providerId": "TERMS_AND_CONDITIONS"}, {"alias": "UPDATE_PASSWORD", "config": {}, "defaultAction": false, "enabled": true, "name": "Update Password", "priority": 30, "providerId": "UPDATE_PASSWORD"}, {"alias": "UPDATE_PROFILE", "config": {}, "defaultAction": false, "enabled": true, "name": "Update Profile", "priority": 40, "providerId": "UPDATE_PROFILE"}, {"alias": "VERIFY_EMAIL", "config": {}, "defaultAction": false, "enabled": true, "name": "<PERSON><PERSON><PERSON>", "priority": 50, "providerId": "VERIFY_EMAIL"}, {"alias": "VERIFY_PROFILE", "config": {}, "defaultAction": false, "enabled": true, "name": "Verify Profile", "priority": 90, "providerId": "VERIFY_PROFILE"}, {"alias": "delete_account", "config": {}, "defaultAction": false, "enabled": false, "name": "Delete Account", "priority": 60, "providerId": "delete_account"}, {"alias": "delete_credential", "config": {}, "defaultAction": false, "enabled": true, "name": "Delete Credential", "priority": 100, "providerId": "delete_credential"}, {"alias": "idp_link", "config": {}, "defaultAction": false, "enabled": true, "name": "Linking Identity Provider", "priority": 110, "providerId": "idp_link"}, {"alias": "update_user_locale", "config": {}, "defaultAction": false, "enabled": true, "name": "Update User Locale", "priority": 1000, "providerId": "update_user_locale"}, {"alias": "webauthn-register", "config": {}, "defaultAction": false, "enabled": true, "name": "Webauthn Register", "priority": 70, "providerId": "webauthn-register"}, {"alias": "webauthn-register-passwordless", "config": {}, "defaultAction": false, "enabled": true, "name": "Webauthn Register Passwordless", "priority": 80, "providerId": "webauthn-register-passwordless"}], "requiredCredentials": ["password"], "resetCredentialsFlow": "reset credentials", "resetPasswordAllowed": false, "revokeRefreshToken": false, "roles": {"client": {"account": [{"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "containerId": "b41743a6-ae46-44a0-9058-7f36791c093b", "description": "${role_manage-account}", "id": "28fcb1b8-66ff-4d9b-9dea-04ef79f853ee", "name": "manage-account"}, {"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "containerId": "b41743a6-ae46-44a0-9058-7f36791c093b", "description": "${role_manage-consent}", "id": "b560008a-4886-4119-aa62-ee6f06130e34", "name": "manage-consent"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "b41743a6-ae46-44a0-9058-7f36791c093b", "description": "${role_delete-account}", "id": "297716b6-515d-4147-8eb4-db368dbc3dcc", "name": "delete-account"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "b41743a6-ae46-44a0-9058-7f36791c093b", "description": "${role_manage-account-links}", "id": "5aef1d8f-a908-46e4-bf39-2c62e05d9c9b", "name": "manage-account-links"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "b41743a6-ae46-44a0-9058-7f36791c093b", "description": "${role_view-applications}", "id": "f3691e9c-22c2-4cef-9127-130d45b37248", "name": "view-applications"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "b41743a6-ae46-44a0-9058-7f36791c093b", "description": "${role_view-consent}", "id": "d1fc4190-a80f-4449-9149-76ff1885d4cd", "name": "view-consent"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "b41743a6-ae46-44a0-9058-7f36791c093b", "description": "${role_view-groups}", "id": "c6ea93f9-d915-4075-b20f-c7f06808f235", "name": "view-groups"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "b41743a6-ae46-44a0-9058-7f36791c093b", "description": "${role_view-profile}", "id": "7f0b159e-7cdd-4cc1-91cd-e2e01fab8492", "name": "view-profile"}], "account-console": [], "admin-cli": [], "broker": [{"attributes": {}, "clientRole": true, "composite": false, "containerId": "afdd58cc-6874-4937-b1c2-7ec8b0b8d699", "description": "${role_read-token}", "id": "6fa97bc6-4b97-492a-bc2b-336ce42cd318", "name": "read-token"}], "onramp-dev-client": [], "realm-management": [{"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"realm-management": ["create-client", "impersonation", "manage-authorization", "manage-clients", "manage-events", "manage-identity-providers", "manage-realm", "manage-users", "query-clients", "query-groups", "query-realms", "query-users", "view-authorization", "view-clients", "view-events", "view-identity-providers", "view-realm", "view-users"]}}, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_realm-admin}", "id": "419404e2-9e2f-4157-b6ca-9b686cc09aff", "name": "realm-admin"}, {"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_view-clients}", "id": "43a654a6-7101-4e5b-ae2e-1782eb6af432", "name": "view-clients"}, {"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_view-users}", "id": "ad2be4ed-26b5-4825-8d67-1aaf165ea1b7", "name": "view-users"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_create-client}", "id": "13480d5d-34d3-4c78-ba14-259293115877", "name": "create-client"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_impersonation}", "id": "67f1a736-273a-44d7-ac53-f6a7e71b6283", "name": "impersonation"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_manage-authorization}", "id": "e68f7802-8707-43a9-b575-d0adc48b8fe8", "name": "manage-authorization"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_manage-clients}", "id": "c1916a45-bc10-4590-a0af-693432789223", "name": "manage-clients"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_manage-events}", "id": "95a3ff5a-5add-48f6-8357-e48631a858ab", "name": "manage-events"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_manage-identity-providers}", "id": "d008a3f3-9a0d-4a62-9d1b-8668a82fae0b", "name": "manage-identity-providers"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_manage-realm}", "id": "5ed7c858-f0b0-4914-a066-36b0feba8026", "name": "manage-realm"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_manage-users}", "id": "33b7ab95-e9ac-4f09-9513-772cba6e240c", "name": "manage-users"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_query-clients}", "id": "29346fcf-591a-4aab-b4b4-f6d1b7ad698a", "name": "query-clients"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_query-groups}", "id": "f0b28c0e-7b9e-46b4-bdc1-34d7cc3da500", "name": "query-groups"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_query-realms}", "id": "eb3c3e0f-7072-4a32-8a23-ba828c89aabb", "name": "query-realms"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_query-users}", "id": "ecdddf8e-f28d-490f-b6cd-a16b91def39b", "name": "query-users"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_view-authorization}", "id": "3eea20e5-693e-4992-94a0-0c939e028671", "name": "view-authorization"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_view-events}", "id": "69c82f02-5525-4e82-92af-4187bea7dc9a", "name": "view-events"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_view-identity-providers}", "id": "f8ddd316-6c71-4d2a-a100-abcabd3d4875", "name": "view-identity-providers"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "4de350ec-447e-47ef-8994-c66739909ac5", "description": "${role_view-realm}", "id": "28ef276e-4c78-4e56-a72a-7aac8bf0b8b4", "name": "view-realm"}], "security-admin-console": []}, "realm": [{"attributes": {}, "clientRole": false, "composite": true, "composites": {"client": {"account": ["manage-account", "view-profile"]}, "realm": ["offline_access", "uma_authorization"]}, "containerId": "3c94ed80-7a88-42fb-8991-2ab7ac590e56", "description": "${role_default-roles}", "id": "d7f25f24-2f23-41b5-8abb-6c52b5b3e2db", "name": "default-roles-onramp-dev"}, {"attributes": {}, "clientRole": false, "composite": false, "containerId": "3c94ed80-7a88-42fb-8991-2ab7ac590e56", "description": "${role_offline-access}", "id": "3cc32a5a-a5d7-4236-8418-c68382d1f07a", "name": "offline_access"}, {"attributes": {}, "clientRole": false, "composite": false, "containerId": "3c94ed80-7a88-42fb-8991-2ab7ac590e56", "description": "${role_uma_authorization}", "id": "fe44ae21-9a5c-4862-bb53-4869eeb21b02", "name": "uma_authorization"}]}, "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "smtpServer": {}, "sslRequired": "external", "ssoSessionIdleTimeout": 1800, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespan": 36000, "ssoSessionMaxLifespanRememberMe": 0, "userManagedAccessAllowed": false, "users": [{"createdTimestamp": 1747783581723, "credentials": [{"createdDate": 1747783592453, "credentialData": "{\"hashIterations\":5,\"algorithm\":\"argon2\",\"additionalParameters\":{\"hashLength\":[\"32\"],\"memory\":[\"7168\"],\"type\":[\"id\"],\"version\":[\"1.3\"],\"parallelism\":[\"1\"]}}", "id": "9cb97f95-e2e3-41dc-b61c-6d95ccb809d0", "secretData": "{\"value\":\"TXdQarAqdVM+mYKMAPcOdHod9rfY5EIw4u0TV2OQo/0=\",\"salt\":\"Cz+c17YaM6BqRn6K9vJ/OQ==\",\"additionalParameters\":{}}", "type": "password", "userLabel": "My password"}], "disableableCredentialTypes": [], "email": "<EMAIL>", "emailVerified": false, "enabled": true, "firstName": "foo", "groups": [], "id": "43a95dc5-0ef2-4b07-b6f4-4a5f6aca9888", "lastName": "foo", "notBefore": 0, "realmRoles": ["default-roles-onramp-dev"], "requiredActions": [], "totp": false, "username": "foo"}, {"createdTimestamp": 1751493442108, "credentials": [{"createdDate": 1751493470465, "credentialData": "{\"hashIterations\":5,\"algorithm\":\"argon2\",\"additionalParameters\":{\"hashLength\":[\"32\"],\"memory\":[\"7168\"],\"type\":[\"id\"],\"version\":[\"1.3\"],\"parallelism\":[\"1\"]}}", "id": "2a4c3df4-cc28-41d8-bb6c-1e22c99139af", "secretData": "{\"value\":\"KElCa4By0bwLL/is6R/3R4BxSlmICs3muD8XarMZ/n0=\",\"salt\":\"bBvTFyNlrcBH4kGfx5rIzg==\",\"additionalParameters\":{}}", "type": "password", "userLabel": "My password"}], "disableableCredentialTypes": [], "email": "<EMAIL>", "emailVerified": true, "enabled": true, "firstName": "Admin", "groups": [], "id": "1b08b49c-804c-4d73-98e8-b23f885a3613", "lastName": "Synapse", "notBefore": 0, "realmRoles": ["default-roles-onramp-dev"], "requiredActions": [], "totp": false, "username": "<EMAIL>"}], "verifiableCredentialsEnabled": false, "verifyEmail": false, "waitIncrementSeconds": 60, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessExtraOrigins": [], "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256", "RS256"], "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicyRpId": "", "webAuthnPolicySignatureAlgorithms": ["ES256", "RS256"], "webAuthnPolicyUserVerificationRequirement": "not specified"}