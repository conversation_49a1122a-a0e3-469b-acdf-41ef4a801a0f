package app

import (
	"context"

	"github.com/gorilla/mux"

	"synapse-its.com/onramp/middlewares"
	"synapse-its.com/onramp/modules/auth"
	"synapse-its.com/shared/api/middleware"
	SharedOidc "synapse-its.com/shared/api/oidc"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/httplogger"
	"synapse-its.com/shared/logger"
)

// NewRouter initializes the router and registers routes.
func NewRouter(connections *connect.Connections, batch bqbatch.Batcher) *mux.Router {
	router := mux.NewRouter()

	// Apply middleware globally (e.g., logging, authentication).
	router.Use(middleware.ConnectionsMiddleware(connections))
	router.Use(middleware.BQBatchMiddleware(batch))
	router.Use(httplogger.LoggingMiddleware)
	router.Use(middlewares.PanicMiddleware)

	// Define default endpoints
	// Note: Assets handler removed as part of migration from SPA to WEB authentication

	// Note: Protected routes are set up in App.Serve() where we have access to the auth handler's session store

	return router
}

// Initialize all OIDC configurations.
func init() {
	ctx := context.Background()
	var err error
	auth.SynapseOIDC, err = SharedOidc.NewConfigFromService(ctx, "Onramp")
	if err != nil {
		logger.Warnf("failed to create base OIDC config: %v", err)
	}

	auth.SynapseOIDCLocal, err = SharedOidc.InitializeLocalConfig(ctx, auth.SynapseOIDC)
	if err != nil {
		logger.Warnf("failed to initialize local OIDC config: %v", err)
	}
}
