package user

import (
	"net/http"

	"github.com/gorilla/mux"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/onramp/middlewares"
	RestUserPermissions "synapse-its.com/onramp/modules/user/permissions"
	userDomain "synapse-its.com/shared/rest/domain/user"
	RestInvites "synapse-its.com/shared/rest/onramp/invites"
	RestAuthMethods "synapse-its.com/shared/rest/onramp/user/auth-methods"
	RestProfile "synapse-its.com/shared/rest/onramp/user/profile"
)

type Handler struct {
	sessionStore domain.SessionStore
	userRepo     userDomain.Repository
}

func NewHandler(sessionStore domain.SessionStore, userRepo userDomain.Repository) *Handler {
	return &Handler{
		sessionStore: sessionStore,
		userRepo:     userRepo,
	}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	// Apply session middleware to user routes
	userRouter := router.PathPrefix("/user").Subrouter()
	userRouter.Use(middlewares.SessionMiddleware(h.sessionStore))

	// Routes that don't require user-specific authorization
	userRouter.HandleFunc("/permissions", RestUserPermissions.Handler).Methods(http.MethodGet)

	// Create a subrouter for routes that require user-specific authorization
	userSpecificRouter := userRouter.PathPrefix("/{userId}").Subrouter()
	userSpecificRouter.Use(middlewares.UserAuthorizationMiddleware())

	// Add new invite routes (require user-specific authorization)
	userSpecificRouter.HandleFunc("/invites", RestInvites.ListUserInvitesForUserHandler).Methods(http.MethodGet)
	userSpecificRouter.HandleFunc("/invites/{inviteId}", RestInvites.RejectInviteHandler).Methods(http.MethodDelete)
	userSpecificRouter.HandleFunc("/invites/{inviteId}/redeem", RestInvites.RedeemInviteHandler).Methods(http.MethodPost)

	// Add auth methods routes (require user-specific authorization)
	userSpecificRouter.HandleFunc("/auth-methods", RestAuthMethods.GetAuthMethodsHandler).Methods(http.MethodGet)
	userSpecificRouter.HandleFunc("/auth-methods/{authMethodId}", RestAuthMethods.DeleteAuthMethodHandler).Methods(http.MethodDelete)
	userSpecificRouter.HandleFunc("/auth-methods/{authMethodId}/password", RestAuthMethods.UpdatePasswordHandler).Methods(http.MethodPatch)

	// Add profile routes (require user-specific authorization)
	userSpecificRouter.HandleFunc("/profile", RestProfile.GetProfileHandler(h.userRepo)).Methods(http.MethodGet)
	userSpecificRouter.HandleFunc("/profile", RestProfile.UpdateProfileHandler(h.userRepo)).Methods(http.MethodPatch)
}
