package user

import (
	"net/http"
	"testing"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	onrampMocks "synapse-its.com/onramp/mock"
	domainMocks "synapse-its.com/shared/rest/domain/mocks"
)

func TestNewHandler(t *testing.T) {
	t.<PERSON>llel()

	// Arrange
	mockSessionStore := &onrampMocks.MockSessionStore{}
	mockUserRepository := &domainMocks.MockUserRepository{}

	// Act
	handler := NewHandler(mockSessionStore, mockUserRepository)

	// Assert
	assert.NotNil(t, handler, "Handler should not be nil")
	assert.Equal(t, mockSessionStore, handler.sessionStore, "Session store should be set correctly")
	assert.Equal(t, mockUserRepository, handler.userRepo, "User repository should be set correctly")
}

func TestHandler_RegisterRoutes(t *testing.T) {
	t.<PERSON>()

	// Arrange
	handler := &Handler{}
	router := mux.NewRouter()

	// Act
	handler.RegisterRoutes(router)

	// Expected routes based on RegisterRoutes implementation
	expectedRoutes := []struct {
		method string
		path   string
	}{
		{
			method: http.MethodGet,
			path:   "/user/permissions",
		},
		{
			method: http.MethodGet,
			path:   "/user/{userId}/invites",
		},
		{
			method: http.MethodDelete,
			path:   "/user/{userId}/invites/{inviteId}",
		},
		{
			method: http.MethodPost,
			path:   "/user/{userId}/invites/{inviteId}/redeem",
		},
		{
			method: http.MethodGet,
			path:   "/user/{userId}/auth-methods",
		},
		{
			method: http.MethodDelete,
			path:   "/user/{userId}/auth-methods/{authMethodId}",
		},
		{
			method: http.MethodPatch,
			path:   "/user/{userId}/auth-methods/{authMethodId}/password",
		},
		{
			method: http.MethodGet,
			path:   "/user/{userId}/profile",
		},
		{
			method: http.MethodPatch,
			path:   "/user/{userId}/profile",
		},
	}

	// Assert
	// Count total routes registered
	var actualRoutes []struct {
		method string
		path   string
	}

	err := router.Walk(func(route *mux.Route, router *mux.Router, ancestors []*mux.Route) error {
		pathTemplate, _ := route.GetPathTemplate()
		methods, _ := route.GetMethods()

		for _, method := range methods {
			actualRoutes = append(actualRoutes, struct {
				method string
				path   string
			}{
				method: method,
				path:   pathTemplate,
			})
		}
		return nil
	})

	assert.NoError(t, err, "Walking routes should not produce an error")

	// Verify expected routes are registered
	assert.Equal(t, len(expectedRoutes), len(actualRoutes), "Should register correct number of routes")

	// Check that all expected routes and methods are present
	for _, expectedRoute := range expectedRoutes {
		found := false
		for _, actualRoute := range actualRoutes {
			if actualRoute.method == expectedRoute.method && actualRoute.path == expectedRoute.path {
				found = true
				break
			}
		}
		assert.True(t, found, "Route %s %s should be registered", expectedRoute.method, expectedRoute.path)
	}
}
