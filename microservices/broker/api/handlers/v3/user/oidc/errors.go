package oidc

import "errors"

// OIDC-related error definitions
var (
	ErrOIDCConfigMissing      = errors.New("OIDC configuration is missing")
	ErrExchangeFailed         = errors.New("failed to exchange authorization code for token")
	ErrMissingIDToken         = errors.New("ID token not found in OAuth2 response")
	ErrInvalidIDToken         = errors.New("failed to verify ID token")
	ErrClaimsExtraction       = errors.New("failed to extract claims from ID token")
	ErrDatabaseConnection     = errors.New("failed to establish database connection")
	ErrPermissionsFailed      = errors.New("failed to create user permissions")
	ErrJWTCreation            = errors.New("failed to create JWT token")
	ErrTokenPersistence       = errors.New("failed to persist token to database")
	ErrMissingSubject         = errors.New("missing subject claim")
	ErrMissingIssuer          = errors.New("missing issuer claim")
	ErrOIDCConfigNotAvailable = errors.New("OIDC configuration not available")
	ErrTokenExchangeFailed    = errors.New("token exchange failed")
	ErrInvalidClaims          = errors.New("invalid claims")
	ErrUserNotFound           = errors.New("user not found")
	ErrUserLookupFailed       = errors.New("user lookup failed")
)
