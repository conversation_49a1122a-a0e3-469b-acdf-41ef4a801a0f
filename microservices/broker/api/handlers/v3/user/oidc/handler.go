package oidc

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"github.com/coreos/go-oidc"
	"golang.org/x/oauth2"
	SharedOidc "synapse-its.com/shared/api/oidc"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/logger"
)

// Dependencies for testing
type HandlerDeps struct {
	TokenExchanger         SharedOidc.OAuth2TokenExchanger
	Service                *Service
	PasswordGrantExchanger func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error)
}

// HandlePasswordGrantWithDeps handles OIDC authentication using Resource Owner Password Credentials Grant
//
// @Summary      OIDC authentication with username/password (API-only)
// @Description  Authenticates users using OIDC Resource Owner Password Credentials Grant flow. This endpoint allows API-only authentication without browser interaction by directly exchanging username/password for JWT tokens.
// @Tags         env:dev, env:qa, env:sandbox, user, oidc
// @Accept       json
// @Produce      json
// @Param        credentials  body      oidc.PasswordGrantRequest  true  "User credentials for authentication"
// @Success      200         {object}  oidc.AuthResponse               "Authentication successful - returns user info and JWT token"
// @Failure      400         {object}  shared.BadRequestResponse       "Bad Request - invalid request body"
// @Failure      401         {object}  shared.UnauthorizedResponse     "Unauthorized - invalid credentials"
// @Failure      500         {object}  shared.InternalServerErrorResponse  "Internal Server Error"
// @Router       /v3/user/oidc/password-grant [post]
func HandlePasswordGrantWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := oidc.ClientContext(r.Context(), SharedOidc.LocalhostHTTPProxy)
		isDev := strings.HasPrefix(r.Host, "localhost:8080")

		// Parse request body
		var req PasswordGrantRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			logger.Errorf("Failed to parse password grant request: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate required fields
		if req.Username == "" || req.Password == "" {
			logger.Error("Missing username or password in password grant request")
			response.CreateBadRequestResponse(w)
			return
		}

		// Process password grant authentication
		authResp, err := deps.processPasswordGrantLogic(ctx, req.Username, req.Password, isDev)
		if err != nil {
			logger.Errorf("Password grant authentication failed: %v", err)
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Return JWT token response
		response.CreateAuthSuccessResponse(authResp, w)
	}
}

// processPasswordGrantLogic handles the Resource Owner Password Credentials Grant flow
func (deps HandlerDeps) processPasswordGrantLogic(ctx context.Context, username, password string, isDev bool) (*AuthResponse, error) {
	// Choose the OIDC configuration based on environment
	oidcConfig := getConfig(isDev)
	if oidcConfig == nil || oidcConfig.OAuth2Config == nil {
		logger.Error("OIDC configuration not available - provider may not be ready")
		return nil, ErrOIDCConfigMissing
	}

	// Create OAuth2 config for password grant
	passwordGrantConfig := &oauth2.Config{
		ClientID:     oidcConfig.OAuth2Config.ClientID,
		ClientSecret: oidcConfig.OAuth2Config.ClientSecret,
		Endpoint: oauth2.Endpoint{
			TokenURL: oidcConfig.OAuth2Config.Endpoint.TokenURL,
		},
		Scopes: oidcConfig.OAuth2Config.Scopes,
	}

	// Exchange username/password for token using Resource Owner Password Credentials Grant
	token, err := deps.PasswordGrantExchanger(ctx, passwordGrantConfig, username, password)
	if err != nil {
		logger.Errorf("Failed to exchange credentials for token: %v", err)
		return nil, ErrExchangeFailed
	}

	// Verify the ID Token and extract claims
	rawID, ok := token.Extra("id_token").(string)
	if !ok {
		logger.Error("ID token not found in OAuth2 response")
		return nil, ErrMissingIDToken
	}

	idToken, err := deps.TokenExchanger.VerifyIDToken(ctx, oidcConfig, rawID)
	if err != nil {
		logger.Errorf("Failed to verify ID token: %v", err)
		return nil, ErrInvalidIDToken
	}

	// Extract claims from ID token
	claims, err := deps.TokenExchanger.ExtractClaims(idToken)
	if err != nil {
		logger.Errorf("Failed to extract claims from ID token: %v", err)
		return nil, ErrClaimsExtraction
	}

	// Extract and validate OIDC claims
	oidcClaims, err := extractOIDCClaims(claims)
	if err != nil {
		logger.Errorf("Failed to extract OIDC claims: %v", err)
		return nil, ErrClaimsExtraction
	}

	// Get database connections
	connections, err := deps.Service.DBProvider(ctx)
	if err != nil {
		logger.Errorf("Failed to get database connections: %v", err)
		return nil, ErrDatabaseConnection
	}
	pg := connections.Postgres

	// Check if user already exists by OIDC subject

	dbUser, err := deps.Service.GetOrCreateOIDCUser(pg, oidcClaims)
	if err != nil {
		logger.Errorf("User lookup/creation failed: %v", err)
		return nil, err
	}

	// Get user permissions
	userPermissions, err := deps.Service.UserPermissionsCreator(pg, dbUser.ID)
	if err != nil {
		logger.Errorf("Failed to create user permissions: %v", err)
		return nil, ErrPermissionsFailed
	}

	// Create JWT token (744 hours = 31 days, same as username/password auth)
	duration := time.Duration(744) * time.Hour
	jwt, expiresAt, err := deps.Service.JwtCreator(oidcClaims.Email, duration, *userPermissions)
	if err != nil {
		logger.Errorf("Failed to create JWT token: %v", err)
		return nil, ErrJWTCreation
	}

	// Persist token to database
	err = deps.Service.TokenPersister(pg, dbUser.ID, jwt, expiresAt)
	if err != nil {
		logger.Errorf("Failed to persist token: %v", err)
		return nil, ErrTokenPersistence
	}

	// Convert database user to API response format
	userInfo := convertDBUserToUserInfo(dbUser, oidcClaims)

	return &AuthResponse{
		User:  *userInfo,
		Token: jwt,
	}, nil
}

func getConfig(isDev bool) *SharedOidc.OIDCConfig {
	if isDev {
		return SynapseOIDCLocal
	}
	return SynapseOIDC
}

// Handler is the production-ready HTTP handler using default dependencies.
var PasswordGrantHandler = HandlePasswordGrantWithDeps(HandlerDeps{
	TokenExchanger: SharedOidc.NewOAuth2TokenExchanger(),
	Service:        NewService(),
	PasswordGrantExchanger: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
		return cfg.PasswordCredentialsToken(ctx, username, password)
	},
})
