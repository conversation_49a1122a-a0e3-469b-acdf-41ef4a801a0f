package oidc

import (
	"github.com/google/uuid"
)

// PasswordGrantRequest represents the request for Resource Owner Password Credentials Grant
type PasswordGrantRequest struct {
	Username string `json:"username,required"`
	Password string `json:"password,required"`
}

// AuthResponse represents the authentication response with JWT token
type AuthResponse struct {
	User  UserInfo `json:"user"`
	Token string   `json:"token"`
}

// UserInfo represents user information from OIDC claims
type UserInfo struct {
	UserID         int64  `json:"user_id"`
	UserIdentifier string `json:"user_identifier"`
	Username       string `json:"username"`
	OrganizationID string `json:"organization_id"`
	APIKey         string `json:"api_key"`
}

// OIDCClaims represents the claims extracted from OIDC ID token
type OIDCClaims struct {
	Sub               string `json:"sub"`
	Issuer            string `json:"iss"`
	Email             string `json:"email"`
	EmailVerified     bool   `json:"email_verified"`
	Name              string `json:"name"`
	GivenName         string `json:"given_name"`
	FamilyName        string `json:"family_name"`
	PreferredUsername string `json:"preferred_username"`
}

// DBUser represents user data from database
type DBUser struct {
	ID                     uuid.UUID `db:"id"`
	OrigID                 int64     `db:"origid"`
	TokenDurationHours     int       `db:"tokendurationhours"`
	OrganizationIdentifier string    `db:"organizationidentifier"`
	APIKey                 string    `db:"apikey"`
	Username               string    `db:"username"`
	Email                  string    `db:"email"`
	FirstName              string    `db:"firstname"`
	LastName               string    `db:"lastname"`
}
