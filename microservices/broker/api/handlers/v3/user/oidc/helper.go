package oidc

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/authtypes"
	"synapse-its.com/shared/api/jwttokens"
	SharedOidc "synapse-its.com/shared/api/oidc"
	"synapse-its.com/shared/api/security"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

var (
	SynapseOIDC      *SharedOidc.OIDCConfig
	SynapseOIDCLocal *SharedOidc.OIDCConfig
)

// Service handles OIDC authentication business logic for broker
type Service struct {
	DBProvider             func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	JwtCreator             func(string, time.Duration, jwttokens.UserPermissions) (string, time.Time, error)
	TokenPersister         func(connect.DatabaseExecutor, uuid.UUID, string, time.Time) error
	UserPermissionsCreator func(connect.DatabaseExecutor, uuid.UUID) (*jwttokens.UserPermissions, error)
	GetOrCreateOIDCUser    func(connect.DatabaseExecutor, *OIDCClaims) (*DBUser, error)
}

// extractOIDCClaims extracts and validates OIDC claims from the token claims map
func extractOIDCClaims(claims map[string]any) (*OIDCClaims, error) {
	oidcClaims := &OIDCClaims{}

	// Extract subject (required)
	if sub, ok := claims["sub"].(string); ok {
		oidcClaims.Sub = sub
	} else {
		return nil, ErrMissingSubject
	}
	if iss, ok := claims["iss"].(string); ok {
		oidcClaims.Issuer = iss
	} else {
		return nil, ErrMissingIssuer
	}

	// Extract email (required for user lookup)
	if email, ok := claims["email"].(string); ok {
		oidcClaims.Email = email
	}

	// Extract optional claims
	if emailVerified, ok := claims["email_verified"].(bool); ok {
		oidcClaims.EmailVerified = emailVerified
	}

	if name, ok := claims["name"].(string); ok {
		oidcClaims.Name = name
	}

	if givenName, ok := claims["given_name"].(string); ok {
		oidcClaims.GivenName = givenName
	}

	if familyName, ok := claims["family_name"].(string); ok {
		oidcClaims.FamilyName = familyName
	}

	if preferredUsername, ok := claims["preferred_username"].(string); ok {
		oidcClaims.PreferredUsername = preferredUsername
	}

	return oidcClaims, nil
}

func getOrCreateOIDCUser(pg connect.DatabaseExecutor, oidcClaims *OIDCClaims) (*DBUser, error) {
	// check if user already exists by OIDC subject
	user, err := lookupUserByOIDCSubject(pg, oidcClaims.Issuer, oidcClaims.Sub)
	if err != nil {
		return nil, err
	}

	if user == nil {
		// Parse name into first/last name
		firstName := ""
		lastName := ""
		if oidcClaims.Name != "" {
			nameParts := strings.Fields(oidcClaims.Name)
			if len(nameParts) > 0 {
				firstName = nameParts[0]
			}
			if len(nameParts) > 1 {
				lastName = strings.Join(nameParts[1:], " ")
			}
		}

		user = &DBUser{
			FirstName: firstName,
			LastName:  lastName,
		}

		// Create user and auth method in database
		if err := createUserAndAuthMethod(pg, user, oidcClaims); err != nil {
			return nil, err
		}
	} else {
		// Update last login for existing user
		if err := updateLastLogin(pg, user.ID); err != nil {
			return nil, err
		}
	}

	return user, nil
}

func updateLastLogin(pg connect.DatabaseExecutor, userID uuid.UUID) error {
	now := time.Now()
	// Update both User.LastLogin and AuthMethod.LastLogin
	query := `
		UPDATE {{User}}
		SET LastLogin = $1, UpdatedAt = $1
		WHERE Id = $2
	`
	_, err := pg.Exec(query, now, userID)
	if err != nil {
		return err
	}

	query = `
		UPDATE {{AuthMethod}}
		SET LastLogin = $1, UpdatedAt = $1	
		WHERE UserId = $2`
	_, err = pg.Exec(query, now, userID)
	if err != nil {
		return err
	}
	return nil
}

func createUserAndAuthMethod(pg connect.DatabaseExecutor, user *DBUser, oidcClaims *OIDCClaims) error {
	query := `
		INSERT INTO {{User}} (
			FirstName, 
			LastName,
			LastLogin
		) VALUES (
			$1, $2, NOW()
		)
		RETURNING Id, OrigId`
	row, err := pg.QueryRow(query, user.FirstName, user.LastName)
	if err != nil {
		return err
	}

	userID, ok := row["id"].(string)
	if !ok {
		return fmt.Errorf("user ID not returned from user creation")
	}

	origID, ok := row["origid"].(int64)
	if !ok {
		return fmt.Errorf("user OrigID not returned from user creation")
	}
	user.OrigID = origID
	// Update the user object with the generated ID
	parsedUUID, parseErr := uuid.Parse(userID)
	if parseErr != nil {
		return fmt.Errorf("failed to parse user ID as UUID: %w", parseErr)
	}
	user.ID = parsedUUID

	// Insert auth method record (AuthMethod ID will be auto-generated)
	query = `
		INSERT INTO {{AuthMethod}} (
			UserId, 
			Type, 
			Sub, 
			Issuer, 
			Email
		) VALUES ($1, $2, $3, $4, $5)`
	_, err = pg.Exec(query, user.ID, authtypes.AuthMethodTypeOIDC, oidcClaims.Sub, oidcClaims.Issuer, oidcClaims.Email)
	if err != nil {
		return err
	}

	return nil
}

// lookupUserByEmail finds a user in the database by email address
func lookupUserByOIDCSubject(pg connect.DatabaseExecutor, issuer, subject string) (*DBUser, error) {
	query := `
			SELECT
			u.Id as id, 
			u.OrigId as origid,
			u.FirstName as firstname, 
			u.LastName as lastname, 
			COALESCE(am.UserName, '') as username,
			COALESCE(am.Email, '') as email,
			COALESCE(m.OrganizationId::text, '') as organizationidentifier
		FROM {{User}} u
		JOIN {{AuthMethod}} am ON u.Id = am.UserId
		LEFT JOIN {{Memberships}} m ON am.Id = m.AuthMethodId AND m.IsDeleted = false
		WHERE am.Issuer = $1 AND am.Sub = $2 AND am.Type = 'OIDC'
		AND NOT u.IsDeleted AND am.IsEnabled = true
		LIMIT 1`
	user := &DBUser{}
	err := pg.QueryRowStruct(user, query, issuer, subject)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		logger.Warnf("User lookup failed for OIDC subject %s: %v", subject, err)
		return nil, ErrUserLookupFailed
	}

	return user, nil
}

// createUserPermissions creates user permissions for JWT token
func createUserPermissions(pg connect.DatabaseExecutor, userID uuid.UUID) (*jwttokens.UserPermissions, error) {
	// Get user's software gateway access
	userSoftwareGatewayAccess := []jwttokens.UserSoftwareGatewayAccess{}
	query := `
		SELECT
			sg.MachineKey
		FROM {{UserSoftwareGateway}} usg
		INNER JOIN {{SoftwareGateway}} sg
			ON usg.SoftwareGatewayId = sg.Id
		WHERE usg.userID = $1`
	err := pg.QueryGenericSlice(&userSoftwareGatewayAccess, query, userID)
	if err != nil {
		logger.Warnf("Failed to get user software gateway permissions for user %s: %v", userID, err)
		return nil, fmt.Errorf("%w: %v", ErrPermissionsFailed, err)
	}

	permissions := &jwttokens.UserPermissions{
		SoftwareGateway: userSoftwareGatewayAccess,
		Device:          []jwttokens.UserDeviceAccess{},
	}

	return permissions, nil
}

// persistTokenToDB saves the JWT token to the database
func persistTokenToDB(pg connect.DatabaseExecutor, userID uuid.UUID, jwt string, expiresAt time.Time) error {
	query := `
		INSERT INTO
			{{UserToken}} (UserId, JWTToken, JWTTokenSha256, Created, Expiration)
		VALUES ($1, $2, $3, $4, $5)`

	_, err := pg.Exec(query, userID, jwt, security.CalculateSHA256(jwt), time.Now().UTC(), expiresAt)
	if err != nil {
		logger.Warnf("Error inserting user token into database: %v", err)
		return ErrTokenPersistence
	}

	return nil
}

// convertDBUserToUserInfo converts database user data to API response format
func convertDBUserToUserInfo(dbUser *DBUser, oidcClaims *OIDCClaims) *UserInfo {
	userInfo := &UserInfo{
		UserID:         dbUser.OrigID,
		UserIdentifier: dbUser.ID.String(),
		Username:       dbUser.Username,
		OrganizationID: dbUser.OrganizationIdentifier,
		APIKey:         dbUser.ID.String(),
	}

	// Use OIDC claims to fill in missing data
	if userInfo.Username == "" && oidcClaims.Email != "" {
		userInfo.Username = oidcClaims.Email
	}

	if userInfo.Username == "" && oidcClaims.GivenName != "" {
		userInfo.Username = oidcClaims.GivenName
	}

	if userInfo.Username == "" && oidcClaims.FamilyName != "" {
		userInfo.Username = oidcClaims.FamilyName
	}

	if userInfo.Username == "" && oidcClaims.PreferredUsername != "" {
		userInfo.Username = oidcClaims.PreferredUsername
	}

	return userInfo
}

// NewService creates a new OIDC service with default dependencies
func NewService() *Service {
	return &Service{
		DBProvider:             connect.GetConnections,
		JwtCreator:             jwttokens.CreateJwtTokenUsingDuration,
		TokenPersister:         persistTokenToDB,
		UserPermissionsCreator: createUserPermissions,
		GetOrCreateOIDCUser:    getOrCreateOIDCUser,
	}
}
