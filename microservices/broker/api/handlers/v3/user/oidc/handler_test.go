package oidc

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/coreos/go-oidc"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"golang.org/x/oauth2"
	"synapse-its.com/shared/api/jwttokens"
	SharedOidc "synapse-its.com/shared/api/oidc"
	"synapse-its.com/shared/connect"
)

// MockOAuth2TokenExchanger implements SharedOidc.OAuth2TokenExchanger for testing
type MockOAuth2TokenExchanger struct {
	VerifyIDTokenFunc func(ctx context.Context, config *SharedOidc.OIDCConfig, rawID string) (*oidc.IDToken, error)
	ExtractClaimsFunc func(idToken *oidc.IDToken) (map[string]any, error)
}

func (m *MockOAuth2TokenExchanger) Exchange(ctx context.Context, config *SharedOidc.OIDCConfig, code string) (*oauth2.Token, error) {
	return &oauth2.Token{}, nil
}

func (m *MockOAuth2TokenExchanger) VerifyIDToken(ctx context.Context, config *SharedOidc.OIDCConfig, rawID string) (*oidc.IDToken, error) {
	if m.VerifyIDTokenFunc != nil {
		return m.VerifyIDTokenFunc(ctx, config, rawID)
	}
	return &oidc.IDToken{}, nil
}

func (m *MockOAuth2TokenExchanger) ExtractClaims(idToken *oidc.IDToken) (map[string]any, error) {
	if m.ExtractClaimsFunc != nil {
		return m.ExtractClaimsFunc(idToken)
	}
	return map[string]any{
		"sub":   "test-subject",
		"iss":   "test-issuer",
		"email": "<EMAIL>",
		"name":  "Test User",
	}, nil
}

// MockService implements Service for testing
type MockService struct {
	DBProviderFunc             func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	JwtCreatorFunc             func(string, time.Duration, jwttokens.UserPermissions) (string, time.Time, error)
	TokenPersisterFunc         func(connect.DatabaseExecutor, uuid.UUID, string, time.Time) error
	UserPermissionsCreatorFunc func(connect.DatabaseExecutor, uuid.UUID) (*jwttokens.UserPermissions, error)
	GetOrCreateOIDCUserFunc    func(connect.DatabaseExecutor, *OIDCClaims) (*DBUser, error)
}

func (m *MockService) DBProvider(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
	if m.DBProviderFunc != nil {
		return m.DBProviderFunc(ctx, checkConnections...)
	}
	return &connect.Connections{}, nil
}

func (m *MockService) JwtCreator(userName string, duration time.Duration, permissions jwttokens.UserPermissions) (string, time.Time, error) {
	if m.JwtCreatorFunc != nil {
		return m.JwtCreatorFunc(userName, duration, permissions)
	}
	return "mock-jwt-token", time.Now().Add(duration), nil
}

func (m *MockService) TokenPersister(pg connect.DatabaseExecutor, userID uuid.UUID, jwt string, expiresAt time.Time) error {
	if m.TokenPersisterFunc != nil {
		return m.TokenPersisterFunc(pg, userID, jwt, expiresAt)
	}
	return nil
}

func (m *MockService) UserPermissionsCreator(pg connect.DatabaseExecutor, userID uuid.UUID) (*jwttokens.UserPermissions, error) {
	if m.UserPermissionsCreatorFunc != nil {
		return m.UserPermissionsCreatorFunc(pg, userID)
	}
	return &jwttokens.UserPermissions{
		SoftwareGateway: []jwttokens.UserSoftwareGatewayAccess{},
		Device:          []jwttokens.UserDeviceAccess{},
	}, nil
}

func (m *MockService) GetOrCreateOIDCUser(pg connect.DatabaseExecutor, oidcClaims *OIDCClaims) (*DBUser, error) {
	if m.GetOrCreateOIDCUserFunc != nil {
		return m.GetOrCreateOIDCUserFunc(pg, oidcClaims)
	}
	return &DBUser{
		ID:        uuid.New(),
		OrigID:    12345,
		Username:  "testuser",
		Email:     "<EMAIL>",
		FirstName: "Test",
		LastName:  "User",
	}, nil
}

func TestPasswordGrantHandler_Success(t *testing.T) {
	t.Parallel()

	// Setup test data
	reqBody := PasswordGrantRequest{
		Username: "testuser",
		Password: "testpass",
	}
	reqBodyBytes, _ := json.Marshal(reqBody)

	// Create request
	req := httptest.NewRequest("POST", "/api/v3/user/oidc/password-grant", strings.NewReader(string(reqBodyBytes)))
	req.Header.Set("Content-Type", "application/json")
	req.Host = "localhost:8080" // Set dev environment

	rr := httptest.NewRecorder()

	// Mock the global OIDC configs
	originalSynapseOIDCLocal := SynapseOIDCLocal
	defer func() { SynapseOIDCLocal = originalSynapseOIDCLocal }()

	SynapseOIDCLocal = &SharedOidc.OIDCConfig{
		OAuth2Config: &oauth2.Config{
			ClientID:     "test-client",
			ClientSecret: "test-secret",
			Endpoint: oauth2.Endpoint{
				TokenURL: "https://test.com/token",
			},
			Scopes: []string{"openid", "email"},
		},
	}

	// Test
	PasswordGrantHandler(rr, req)

	// Verify - should return 401 due to OAuth2 token exchange failure in test environment
	assert.Equal(t, http.StatusUnauthorized, rr.Code)
}

func TestPasswordGrantHandler_InvalidJSON(t *testing.T) {
	t.Parallel()

	// Create request with invalid JSON
	req := httptest.NewRequest("POST", "/api/v3/user/oidc/password-grant", strings.NewReader("invalid json"))
	req.Header.Set("Content-Type", "application/json")

	rr := httptest.NewRecorder()

	// Test
	PasswordGrantHandler(rr, req)

	// Verify
	assert.Equal(t, http.StatusBadRequest, rr.Code)
}

func TestPasswordGrantHandler_MissingCredentials(t *testing.T) {
	t.Parallel()

	// Setup test data with missing password
	reqBody := PasswordGrantRequest{
		Username: "testuser",
		Password: "", // Missing password
	}
	reqBodyBytes, _ := json.Marshal(reqBody)

	// Create request
	req := httptest.NewRequest("POST", "/api/v3/user/oidc/password-grant", strings.NewReader(string(reqBodyBytes)))
	req.Header.Set("Content-Type", "application/json")

	rr := httptest.NewRecorder()

	// Test
	PasswordGrantHandler(rr, req)

	// Verify
	assert.Equal(t, http.StatusBadRequest, rr.Code)
}

func TestHandlePasswordGrantWithDeps_Success(t *testing.T) {
	t.Parallel()

	// Setup test data
	reqBody := PasswordGrantRequest{
		Username: "testuser",
		Password: "testpass",
	}
	reqBodyBytes, _ := json.Marshal(reqBody)

	// Create request
	req := httptest.NewRequest("POST", "/api/v3/user/oidc/password-grant", strings.NewReader(string(reqBodyBytes)))
	req.Header.Set("Content-Type", "application/json")
	req.Host = "localhost:8080" // Set dev environment

	rr := httptest.NewRecorder()

	// Mock dependencies
	mockTokenExchanger := &MockOAuth2TokenExchanger{}
	mockService := &MockService{}

	deps := HandlerDeps{
		TokenExchanger: mockTokenExchanger,
		PasswordGrantExchanger: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
			return cfg.PasswordCredentialsToken(ctx, username, password)
		},
		Service: &Service{
			DBProvider:             mockService.DBProvider,
			JwtCreator:             mockService.JwtCreator,
			TokenPersister:         mockService.TokenPersister,
			UserPermissionsCreator: mockService.UserPermissionsCreator,
			GetOrCreateOIDCUser:    mockService.GetOrCreateOIDCUser,
		},
	}

	// Mock the global OIDC configs
	originalSynapseOIDCLocal := SynapseOIDCLocal
	defer func() { SynapseOIDCLocal = originalSynapseOIDCLocal }()

	SynapseOIDCLocal = &SharedOidc.OIDCConfig{
		OAuth2Config: &oauth2.Config{
			ClientID:     "test-client",
			ClientSecret: "test-secret",
			Endpoint: oauth2.Endpoint{
				TokenURL: "https://test.com/token",
			},
			Scopes: []string{"openid", "email"},
		},
	}

	// Test
	handler := HandlePasswordGrantWithDeps(deps)
	handler(rr, req)

	// Verify - should return 401 due to OAuth2 token exchange failure in test environment
	assert.Equal(t, http.StatusUnauthorized, rr.Code)
}

func TestGetConfig_DevEnvironment(t *testing.T) {
	t.Parallel()

	// Setup
	originalSynapseOIDCLocal := SynapseOIDCLocal
	defer func() { SynapseOIDCLocal = originalSynapseOIDCLocal }()

	expectedConfig := &SharedOidc.OIDCConfig{}
	SynapseOIDCLocal = expectedConfig

	// Test
	config := getConfig(true)

	// Verify
	assert.Equal(t, expectedConfig, config)
}

func TestGetConfig_ProdEnvironment(t *testing.T) {
	t.Parallel()

	// Setup
	originalSynapseOIDC := SynapseOIDC
	defer func() { SynapseOIDC = originalSynapseOIDC }()

	expectedConfig := &SharedOidc.OIDCConfig{}
	SynapseOIDC = expectedConfig

	// Test
	config := getConfig(false)

	// Verify
	assert.Equal(t, expectedConfig, config)
}

func TestPasswordGrantHandler_ReturnsJWTOnSuccess(t *testing.T) {
	t.Parallel()

	// Setup request body.
	reqBody := PasswordGrantRequest{Username: "testuser", Password: "testpass"}
	reqBodyBytes, _ := json.Marshal(reqBody)

	// Create request and recorder.
	req := httptest.NewRequest("POST", "/api/v3/user/oidc/password-grant", strings.NewReader(string(reqBodyBytes)))
	req.Header.Set("Content-Type", "application/json")
	req.Host = "localhost:8080"
	rr := httptest.NewRecorder()

	// Mock OIDC config.
	oldLocal := SynapseOIDCLocal
	defer func() { SynapseOIDCLocal = oldLocal }()
	SynapseOIDCLocal = &SharedOidc.OIDCConfig{OAuth2Config: &oauth2.Config{ClientID: "cid", ClientSecret: "sec", Endpoint: oauth2.Endpoint{TokenURL: "http://unit-test/token"}, Scopes: []string{"openid"}}}

	// Mocks for dependencies.
	mockTokenExchanger := &MockOAuth2TokenExchanger{
		VerifyIDTokenFunc: func(ctx context.Context, config *SharedOidc.OIDCConfig, rawID string) (*oidc.IDToken, error) {
			return &oidc.IDToken{}, nil
		},
		ExtractClaimsFunc: func(idToken *oidc.IDToken) (map[string]any, error) {
			return map[string]any{"sub": "s", "iss": "i", "email": "<EMAIL>", "name": "Test User"}, nil
		},
	}
	mockService := &MockService{
		DBProviderFunc: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
			return &connect.Connections{}, nil
		},
		JwtCreatorFunc: func(userName string, duration time.Duration, permissions jwttokens.UserPermissions) (string, time.Time, error) {
			return "mock-jwt-token-ok", time.Now().Add(duration), nil
		},
		TokenPersisterFunc: func(pg connect.DatabaseExecutor, userID uuid.UUID, jwt string, expiresAt time.Time) error { return nil },
		UserPermissionsCreatorFunc: func(pg connect.DatabaseExecutor, userID uuid.UUID) (*jwttokens.UserPermissions, error) {
			return &jwttokens.UserPermissions{}, nil
		},
		GetOrCreateOIDCUserFunc: func(pg connect.DatabaseExecutor, oidcClaims *OIDCClaims) (*DBUser, error) {
			return &DBUser{ID: uuid.New(), OrigID: 1, Username: "testuser", Email: "<EMAIL>", FirstName: "Test", LastName: "User"}, nil
		},
	}

	deps := HandlerDeps{
		TokenExchanger: mockTokenExchanger,
		Service: &Service{
			DBProvider:             mockService.DBProvider,
			JwtCreator:             mockService.JwtCreator,
			TokenPersister:         mockService.TokenPersister,
			UserPermissionsCreator: mockService.UserPermissionsCreator,
			GetOrCreateOIDCUser:    mockService.GetOrCreateOIDCUser,
		},
		PasswordGrantExchanger: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
			// Return a token that contains an id_token for downstream logic.
			tok := &oauth2.Token{}
			return tok.WithExtra(map[string]any{"id_token": "dummy-id-token"}), nil
		},
	}

	// Invoke handler with injected deps.
	h := HandlePasswordGrantWithDeps(deps)
	h(rr, req)

	// Assert OK and response body has token and user fields inside data wrapper.
	assert.Equal(t, http.StatusOK, rr.Code, "should return 200 on successful flow")
	var envelope struct {
		Status string       `json:"status"`
		Data   AuthResponse `json:"data"`
	}
	assert.NoError(t, json.Unmarshal(rr.Body.Bytes(), &envelope))
	assert.Equal(t, "success", envelope.Status)
	assert.Equal(t, "mock-jwt-token-ok", envelope.Data.Token)
	assert.Equal(t, "testuser", envelope.Data.User.Username)
	assert.NotEmpty(t, envelope.Data.User.UserIdentifier)
	assert.NotZero(t, envelope.Data.User.UserID)
}

func TestProcessPasswordGrantLogic_OIDCConfigMissing(t *testing.T) {
	t.Parallel()

	// Ensure dev OIDC config is nil.
	oldLocal := SynapseOIDCLocal
	defer func() { SynapseOIDCLocal = oldLocal }()
	SynapseOIDCLocal = nil

	deps := HandlerDeps{}
	_, err := deps.processPasswordGrantLogic(context.Background(), "user", "pass", true)
	assert.ErrorIs(t, err, ErrOIDCConfigMissing, "should return ErrOIDCConfigMissing when OIDC config is nil")
}

func TestProcessPasswordGrantLogic_ErrorPaths(t *testing.T) {
	t.Parallel()

	type mocks struct {
		pgEx    func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error)
		verify  func(ctx context.Context, config *SharedOidc.OIDCConfig, rawID string) (*oidc.IDToken, error)
		extract func(idToken *oidc.IDToken) (map[string]any, error)
		dbProv  func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
		getUser func(connect.DatabaseExecutor, *OIDCClaims) (*DBUser, error)
		perm    func(connect.DatabaseExecutor, uuid.UUID) (*jwttokens.UserPermissions, error)
		jwt     func(string, time.Duration, jwttokens.UserPermissions) (string, time.Time, error)
		persist func(connect.DatabaseExecutor, uuid.UUID, string, time.Time) error
	}

	tests := []struct {
		name      string
		mk        mocks
		expectErr error
	}{
		{
			name: "exchange_error",
			mk: mocks{pgEx: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
				return nil, assert.AnError
			}},
			expectErr: ErrExchangeFailed,
		},
		{
			name: "missing_id_token",
			mk: mocks{pgEx: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
				return (&oauth2.Token{}).WithExtra(map[string]any{}), nil
			}},
			expectErr: ErrMissingIDToken,
		},
		{
			name: "verify_id_token_error",
			mk: mocks{
				pgEx: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
					return (&oauth2.Token{}).WithExtra(map[string]any{"id_token": "x"}), nil
				},
				verify: func(ctx context.Context, config *SharedOidc.OIDCConfig, rawID string) (*oidc.IDToken, error) {
					return nil, assert.AnError
				},
			},
			expectErr: ErrInvalidIDToken,
		},
		{
			name: "extract_claims_error",
			mk: mocks{
				pgEx: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
					return (&oauth2.Token{}).WithExtra(map[string]any{"id_token": "x"}), nil
				},
				verify: func(ctx context.Context, config *SharedOidc.OIDCConfig, rawID string) (*oidc.IDToken, error) {
					return &oidc.IDToken{}, nil
				},
				extract: func(idToken *oidc.IDToken) (map[string]any, error) { return nil, assert.AnError },
			},
			expectErr: ErrClaimsExtraction,
		},
		{
			name: "extract_oidc_claims_error_missing_sub",
			mk: mocks{
				pgEx: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
					return (&oauth2.Token{}).WithExtra(map[string]any{"id_token": "x"}), nil
				},
				verify: func(ctx context.Context, config *SharedOidc.OIDCConfig, rawID string) (*oidc.IDToken, error) {
					return &oidc.IDToken{}, nil
				},
				extract: func(idToken *oidc.IDToken) (map[string]any, error) { return map[string]any{"iss": "issuer"}, nil },
			},
			expectErr: ErrClaimsExtraction,
		},
		{
			name: "db_provider_error",
			mk: mocks{
				pgEx: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
					return (&oauth2.Token{}).WithExtra(map[string]any{"id_token": "x"}), nil
				},
				verify: func(ctx context.Context, config *SharedOidc.OIDCConfig, rawID string) (*oidc.IDToken, error) {
					return &oidc.IDToken{}, nil
				},
				extract: func(idToken *oidc.IDToken) (map[string]any, error) {
					return map[string]any{"sub": "s", "iss": "i", "email": "<EMAIL>"}, nil
				},
				dbProv: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return nil, assert.AnError
				},
			},
			expectErr: ErrDatabaseConnection,
		},
		{
			name: "get_or_create_user_error",
			mk: mocks{
				pgEx: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
					return (&oauth2.Token{}).WithExtra(map[string]any{"id_token": "x"}), nil
				},
				verify: func(ctx context.Context, config *SharedOidc.OIDCConfig, rawID string) (*oidc.IDToken, error) {
					return &oidc.IDToken{}, nil
				},
				extract: func(idToken *oidc.IDToken) (map[string]any, error) {
					return map[string]any{"sub": "s", "iss": "i", "email": "<EMAIL>"}, nil
				},
				dbProv: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				getUser: func(connect.DatabaseExecutor, *OIDCClaims) (*DBUser, error) { return nil, assert.AnError },
			},
			expectErr: assert.AnError,
		},
		{
			name: "permissions_error",
			mk: mocks{
				pgEx: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
					return (&oauth2.Token{}).WithExtra(map[string]any{"id_token": "x"}), nil
				},
				verify: func(ctx context.Context, config *SharedOidc.OIDCConfig, rawID string) (*oidc.IDToken, error) {
					return &oidc.IDToken{}, nil
				},
				extract: func(idToken *oidc.IDToken) (map[string]any, error) {
					return map[string]any{"sub": "s", "iss": "i", "email": "<EMAIL>"}, nil
				},
				dbProv: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				getUser: func(connect.DatabaseExecutor, *OIDCClaims) (*DBUser, error) {
					return &DBUser{ID: uuid.New(), OrigID: 1, Username: "u"}, nil
				},
				perm: func(connect.DatabaseExecutor, uuid.UUID) (*jwttokens.UserPermissions, error) {
					return nil, assert.AnError
				},
			},
			expectErr: ErrPermissionsFailed,
		},
		{
			name: "jwt_creation_error",
			mk: mocks{
				pgEx: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
					return (&oauth2.Token{}).WithExtra(map[string]any{"id_token": "x"}), nil
				},
				verify: func(ctx context.Context, config *SharedOidc.OIDCConfig, rawID string) (*oidc.IDToken, error) {
					return &oidc.IDToken{}, nil
				},
				extract: func(idToken *oidc.IDToken) (map[string]any, error) {
					return map[string]any{"sub": "s", "iss": "i", "email": "<EMAIL>"}, nil
				},
				dbProv: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				getUser: func(connect.DatabaseExecutor, *OIDCClaims) (*DBUser, error) {
					return &DBUser{ID: uuid.New(), OrigID: 1, Username: "u"}, nil
				},
				perm: func(connect.DatabaseExecutor, uuid.UUID) (*jwttokens.UserPermissions, error) {
					return &jwttokens.UserPermissions{}, nil
				},
				jwt: func(string, time.Duration, jwttokens.UserPermissions) (string, time.Time, error) {
					return "", time.Time{}, assert.AnError
				},
			},
			expectErr: ErrJWTCreation,
		},
		{
			name: "token_persist_error",
			mk: mocks{
				pgEx: func(ctx context.Context, cfg *oauth2.Config, username, password string) (*oauth2.Token, error) {
					return (&oauth2.Token{}).WithExtra(map[string]any{"id_token": "x"}), nil
				},
				verify: func(ctx context.Context, config *SharedOidc.OIDCConfig, rawID string) (*oidc.IDToken, error) {
					return &oidc.IDToken{}, nil
				},
				extract: func(idToken *oidc.IDToken) (map[string]any, error) {
					return map[string]any{"sub": "s", "iss": "i", "email": "<EMAIL>"}, nil
				},
				dbProv: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				getUser: func(connect.DatabaseExecutor, *OIDCClaims) (*DBUser, error) {
					return &DBUser{ID: uuid.New(), OrigID: 1, Username: "u"}, nil
				},
				perm: func(connect.DatabaseExecutor, uuid.UUID) (*jwttokens.UserPermissions, error) {
					return &jwttokens.UserPermissions{}, nil
				},
				jwt: func(string, time.Duration, jwttokens.UserPermissions) (string, time.Time, error) {
					return "jwt", time.Now(), nil
				},
				persist: func(connect.DatabaseExecutor, uuid.UUID, string, time.Time) error { return assert.AnError },
			},
			expectErr: ErrTokenPersistence,
		},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// OIDC config present.
			oldLocal := SynapseOIDCLocal
			defer func() { SynapseOIDCLocal = oldLocal }()
			SynapseOIDCLocal = &SharedOidc.OIDCConfig{OAuth2Config: &oauth2.Config{ClientID: "cid", ClientSecret: "sec", Endpoint: oauth2.Endpoint{TokenURL: "http://unit-test/token"}, Scopes: []string{"openid"}}}

			mockTokenExchanger := &MockOAuth2TokenExchanger{}
			if tc.mk.verify != nil {
				mockTokenExchanger.VerifyIDTokenFunc = tc.mk.verify
			}
			if tc.mk.extract != nil {
				mockTokenExchanger.ExtractClaimsFunc = tc.mk.extract
			}

			mockService := &MockService{}
			if tc.mk.dbProv != nil {
				mockService.DBProviderFunc = tc.mk.dbProv
			}
			if tc.mk.getUser != nil {
				mockService.GetOrCreateOIDCUserFunc = tc.mk.getUser
			}
			if tc.mk.perm != nil {
				mockService.UserPermissionsCreatorFunc = tc.mk.perm
			}
			if tc.mk.jwt != nil {
				mockService.JwtCreatorFunc = tc.mk.jwt
			}
			if tc.mk.persist != nil {
				mockService.TokenPersisterFunc = tc.mk.persist
			}

			deps := HandlerDeps{
				TokenExchanger: mockTokenExchanger,
				Service: &Service{
					DBProvider:             mockService.DBProvider,
					JwtCreator:             mockService.JwtCreator,
					TokenPersister:         mockService.TokenPersister,
					UserPermissionsCreator: mockService.UserPermissionsCreator,
					GetOrCreateOIDCUser:    mockService.GetOrCreateOIDCUser,
				},
			}
			if tc.mk.pgEx != nil {
				deps.PasswordGrantExchanger = tc.mk.pgEx
			}

			_, err := deps.processPasswordGrantLogic(context.Background(), "user", "pass", true)
			assert.Error(t, err)
			assert.ErrorIs(t, err, tc.expectErr)
		})
	}
}
