package oidc

import (
	"database/sql"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/jwttokens"
)

// MockDatabaseExecutor for testing database operations
type MockDatabaseExecutor struct {
	QueryRowStructFunc    func(dest interface{}, query string, args ...interface{}) error
	QueryGenericSliceFunc func(dest interface{}, query string, args ...interface{}) error
	ExecFunc              func(query string, args ...interface{}) (sql.Result, error)
	QueryRowFunc          func(query string, args ...interface{}) (map[string]interface{}, error)
	QueryGenericFunc      func(query string, args ...interface{}) ([]map[string]interface{}, error)
	ExecMultipleFunc      func(queries string) error
	EscapeIdentifierFunc  func(identifier string) string
	ReplaceNamespaceFunc  func(query string) string
}

func (m *MockDatabaseExecutor) QueryRowStruct(dest interface{}, query string, args ...interface{}) error {
	if m.QueryRowStructFunc != nil {
		return m.QueryRowStructFunc(dest, query, args...)
	}
	return nil
}

func (m *MockDatabaseExecutor) QueryGenericSlice(dest interface{}, query string, args ...interface{}) error {
	if m.QueryGenericSliceFunc != nil {
		return m.QueryGenericSliceFunc(dest, query, args...)
	}
	return nil
}

func (m *MockDatabaseExecutor) Exec(query string, args ...interface{}) (sql.Result, error) {
	if m.ExecFunc != nil {
		return m.ExecFunc(query, args...)
	}
	return &mockResult{rowsAffected: 1}, nil
}

// mockResult implements sql.Result for testing
type mockResult struct {
	rowsAffected int64
	lastInsertId int64
}

func (m *mockResult) LastInsertId() (int64, error) {
	return m.lastInsertId, nil
}

func (m *mockResult) RowsAffected() (int64, error) {
	return m.rowsAffected, nil
}

func (m *MockDatabaseExecutor) QueryRow(query string, args ...interface{}) (map[string]interface{}, error) {
	if m.QueryRowFunc != nil {
		return m.QueryRowFunc(query, args...)
	}
	return map[string]interface{}{"id": uuid.New().String()}, nil
}

func (m *MockDatabaseExecutor) Close() error {
	return nil
}

func (m *MockDatabaseExecutor) QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error) {
	if m.QueryGenericFunc != nil {
		return m.QueryGenericFunc(query, args...)
	}
	return []map[string]interface{}{}, nil
}

func (m *MockDatabaseExecutor) ExecMultiple(queries string) error {
	if m.ExecMultipleFunc != nil {
		return m.ExecMultipleFunc(queries)
	}
	return nil
}

func (m *MockDatabaseExecutor) EscapeIdentifier(identifier string) string {
	if m.EscapeIdentifierFunc != nil {
		return m.EscapeIdentifierFunc(identifier)
	}
	return identifier
}

func (m *MockDatabaseExecutor) ReplaceNamespace(query string) string {
	if m.ReplaceNamespaceFunc != nil {
		return m.ReplaceNamespaceFunc(query)
	}
	return query
}

func TestExtractOIDCClaims(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		claims      map[string]any
		expected    *OIDCClaims
		expectedErr error
		wantErr     bool
	}{
		{
			name: "valid_claims_with_all_fields",
			claims: map[string]any{
				"sub":                "test-subject",
				"iss":                "test-issuer",
				"email":              "<EMAIL>",
				"email_verified":     true,
				"name":               "Test User",
				"given_name":         "Test",
				"family_name":        "User",
				"preferred_username": "testuser",
			},
			expected: &OIDCClaims{
				Sub:               "test-subject",
				Issuer:            "test-issuer",
				Email:             "<EMAIL>",
				EmailVerified:     true,
				Name:              "Test User",
				GivenName:         "Test",
				FamilyName:        "User",
				PreferredUsername: "testuser",
			},
			wantErr: false,
		},
		{
			name: "minimal_required_claims",
			claims: map[string]any{
				"sub":   "test-subject",
				"iss":   "test-issuer",
				"email": "<EMAIL>",
			},
			expected: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
			},
			wantErr: false,
		},
		{
			name: "missing_subject_claim",
			claims: map[string]any{
				"iss":   "test-issuer",
				"email": "<EMAIL>",
			},
			expected:    nil,
			expectedErr: ErrMissingSubject,
			wantErr:     true,
		},
		{
			name: "missing_issuer_claim",
			claims: map[string]any{
				"sub":   "test-subject",
				"email": "<EMAIL>",
			},
			expected:    nil,
			expectedErr: ErrMissingIssuer,
			wantErr:     true,
		},
		{
			name:        "empty_claims_map",
			claims:      map[string]any{},
			expected:    nil,
			expectedErr: ErrMissingSubject,
			wantErr:     true,
		},
		{
			name:        "nil_claims",
			claims:      nil,
			expected:    nil,
			expectedErr: ErrMissingSubject,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Test
			result, err := extractOIDCClaims(tt.claims)

			// Verify
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.expectedErr != nil {
					assert.ErrorIs(t, err, tt.expectedErr)
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestConvertDBUserToUserInfo(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name       string
		dbUser     *DBUser
		oidcClaims *OIDCClaims
		expected   *UserInfo
		setupFn    func() *DBUser
	}{
		{
			name: "complete_user_data_with_username",
			setupFn: func() *DBUser {
				userID := uuid.New()
				return &DBUser{
					ID:                     userID,
					OrigID:                 12345,
					OrganizationIdentifier: "org-123",
					Username:               "testuser",
					Email:                  "<EMAIL>",
				}
			},
			oidcClaims: &OIDCClaims{
				Email: "<EMAIL>",
			},
			expected: &UserInfo{
				UserID:         12345,
				Username:       "testuser",
				OrganizationID: "org-123",
			},
		},
		{
			name: "user_with_oidc_email_fallback",
			setupFn: func() *DBUser {
				userID := uuid.New()
				return &DBUser{
					ID:                     userID,
					OrigID:                 12345,
					OrganizationIdentifier: "org-123",
					Username:               "",
					Email:                  "",
				}
			},
			oidcClaims: &OIDCClaims{
				Email: "<EMAIL>",
			},
			expected: &UserInfo{
				UserID:         12345,
				Username:       "<EMAIL>",
				OrganizationID: "org-123",
			},
		},
		{
			name: "user_with_given_name_fallback",
			setupFn: func() *DBUser {
				userID := uuid.New()
				return &DBUser{
					ID:                     userID,
					OrigID:                 12345,
					OrganizationIdentifier: "org-123",
					Username:               "",
					Email:                  "",
				}
			},
			oidcClaims: &OIDCClaims{
				GivenName: "John",
			},
			expected: &UserInfo{
				UserID:         12345,
				Username:       "John",
				OrganizationID: "org-123",
			},
		},
		{
			name: "user_with_family_name_fallback",
			setupFn: func() *DBUser {
				userID := uuid.New()
				return &DBUser{
					ID:                     userID,
					OrigID:                 12345,
					OrganizationIdentifier: "org-123",
					Username:               "",
					Email:                  "",
				}
			},
			oidcClaims: &OIDCClaims{
				FamilyName: "Doe",
			},
			expected: &UserInfo{
				UserID:         12345,
				Username:       "Doe",
				OrganizationID: "org-123",
			},
		},
		{
			name: "user_with_preferred_username_fallback",
			setupFn: func() *DBUser {
				userID := uuid.New()
				return &DBUser{
					ID:                     userID,
					OrigID:                 12345,
					OrganizationIdentifier: "org-123",
					Username:               "",
					Email:                  "",
				}
			},
			oidcClaims: &OIDCClaims{
				PreferredUsername: "johndoe",
			},
			expected: &UserInfo{
				UserID:         12345,
				Username:       "johndoe",
				OrganizationID: "org-123",
			},
		},
		{
			name: "user_with_multiple_fallback_options",
			setupFn: func() *DBUser {
				userID := uuid.New()
				return &DBUser{
					ID:                     userID,
					OrigID:                 12345,
					OrganizationIdentifier: "org-123",
					Username:               "",
					Email:                  "",
				}
			},
			oidcClaims: &OIDCClaims{
				Email:             "<EMAIL>",
				GivenName:         "John",
				FamilyName:        "Doe",
				PreferredUsername: "johndoe",
			},
			expected: &UserInfo{
				UserID:         12345,
				Username:       "<EMAIL>", // Email takes priority
				OrganizationID: "org-123",
			},
		},
		{
			name: "user_with_no_fallback_options",
			setupFn: func() *DBUser {
				userID := uuid.New()
				return &DBUser{
					ID:                     userID,
					OrigID:                 12345,
					OrganizationIdentifier: "org-123",
					Username:               "",
					Email:                  "",
				}
			},
			oidcClaims: &OIDCClaims{},
			expected: &UserInfo{
				UserID:         12345,
				Username:       "", // No fallback available
				OrganizationID: "org-123",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			dbUser := tt.setupFn()

			// Test
			result := convertDBUserToUserInfo(dbUser, tt.oidcClaims)

			// Verify
			assert.Equal(t, tt.expected.UserID, result.UserID)
			assert.Equal(t, tt.expected.Username, result.Username)
			assert.Equal(t, tt.expected.OrganizationID, result.OrganizationID)
			assert.Equal(t, dbUser.ID.String(), result.UserIdentifier)
			assert.Equal(t, dbUser.ID.String(), result.APIKey)
		})
	}
}

func TestNewService(t *testing.T) {
	t.Parallel()

	// Test
	service := NewService()

	// Verify
	assert.NotNil(t, service.DBProvider, "DBProvider should be set")
	assert.NotNil(t, service.JwtCreator, "JwtCreator should be set")
	assert.NotNil(t, service.TokenPersister, "TokenPersister should be set")
	assert.NotNil(t, service.UserPermissionsCreator, "UserPermissionsCreator should be set")
	assert.NotNil(t, service.GetOrCreateOIDCUser, "GetOrCreateOIDCUser should be set")
}

func TestGetOrCreateOIDCUser(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		oidcClaims  *OIDCClaims
		expected    *DBUser
		expectedErr error
		wantErr     bool
		setupFn     func() *MockDatabaseExecutor
	}{
		{
			name: "existing_user_found",
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
			},
			expected: &DBUser{
				Username:               "existinguser",
				Email:                  "<EMAIL>",
				FirstName:              "Existing",
				LastName:               "User",
				OrganizationIdentifier: "org-123",
			},
			wantErr: false,
			setupFn: func() *MockDatabaseExecutor {
				userID := uuid.New()
				return &MockDatabaseExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						user := dest.(*DBUser)
						user.ID = userID
						user.OrigID = 12345
						user.Username = "existinguser"
						user.Email = "<EMAIL>"
						user.FirstName = "Existing"
						user.LastName = "User"
						user.OrganizationIdentifier = "org-123"
						return nil
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return &mockResult{rowsAffected: 1}, nil
					},
				}
			},
		},
		{
			name: "new_user_created_successfully",
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
				Name:   "New User",
			},
			expected: &DBUser{
				FirstName: "New",
				LastName:  "User",
			},
			wantErr: false,
			setupFn: func() *MockDatabaseExecutor {
				userID := uuid.New()
				return &MockDatabaseExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return sql.ErrNoRows // No user found
					},
					QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
						return map[string]interface{}{
							"id":     userID.String(),
							"origid": int64(12345),
						}, nil
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return &mockResult{rowsAffected: 1}, nil
					},
				}
			},
		},
		{
			name: "user_lookup_error",
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
				Name:   "New User",
			},
			expected:    nil,
			expectedErr: ErrUserLookupFailed,
			wantErr:     true,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return assert.AnError
					},
				}
			},
		},
		{
			name: "user_creation_error",
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
				Name:   "New User",
			},
			expected: nil,
			wantErr:  true,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return sql.ErrNoRows // No user found
					},
					QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
						return nil, assert.AnError // User creation fails
					},
				}
			},
		},
		{
			name: "update_last_login_error",
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
			},
			expected: nil,
			wantErr:  true,
			setupFn: func() *MockDatabaseExecutor {
				userID := uuid.New()
				return &MockDatabaseExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						user := dest.(*DBUser)
						user.ID = userID
						user.OrigID = 12345
						user.Username = "existinguser"
						user.Email = "<EMAIL>"
						user.FirstName = "Existing"
						user.LastName = "User"
						user.OrganizationIdentifier = "org-123"
						return nil
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return nil, assert.AnError // Update fails
					},
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockDB := tt.setupFn()

			// Test
			result, err := getOrCreateOIDCUser(mockDB, tt.oidcClaims)

			// Verify
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.expectedErr != nil {
					assert.ErrorIs(t, err, tt.expectedErr)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if tt.expected != nil {
					assert.Equal(t, tt.expected.Username, result.Username)
					assert.Equal(t, tt.expected.Email, result.Email)
					assert.Equal(t, tt.expected.FirstName, result.FirstName)
					assert.Equal(t, tt.expected.LastName, result.LastName)
					assert.Equal(t, tt.expected.OrganizationIdentifier, result.OrganizationIdentifier)
				}
			}
		})
	}
}

func TestUpdateLastLogin(t *testing.T) {
	t.Parallel()

	userID := uuid.New()
	callCount := 0

	// Mock database executor
	mockDB := &MockDatabaseExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			callCount++
			return &mockResult{rowsAffected: 1}, nil
		},
	}

	// Test
	err := updateLastLogin(mockDB, userID)

	// Verify
	assert.NoError(t, err)
	assert.Equal(t, 2, callCount, "Should call Exec twice (User and AuthMethod updates)")
}

func TestCreateUserAndAuthMethod(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		user        *DBUser
		oidcClaims  *OIDCClaims
		expectedErr error
		wantErr     bool
		setupFn     func() *MockDatabaseExecutor
		verifyFn    func(t *testing.T, user *DBUser, err error)
	}{
		{
			name: "successful_user_creation",
			user: &DBUser{
				FirstName: "Test",
				LastName:  "User",
			},
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
			},
			wantErr: false,
			setupFn: func() *MockDatabaseExecutor {
				userID := uuid.New()
				return &MockDatabaseExecutor{
					QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
						return map[string]interface{}{
							"id":     userID.String(),
							"origid": int64(12345),
						}, nil
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return &mockResult{rowsAffected: 1}, nil
					},
				}
			},
			verifyFn: func(t *testing.T, user *DBUser, err error) {
				assert.NoError(t, err)
				assert.NotEqual(t, uuid.Nil, user.ID)
				assert.Equal(t, int64(12345), user.OrigID)
			},
		},
		{
			name: "invalid_user_id_type",
			user: &DBUser{
				FirstName: "Test",
				LastName:  "User",
			},
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
			},
			wantErr: true,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
						return map[string]interface{}{"id": 123}, nil // Invalid type
					},
				}
			},
			verifyFn: func(t *testing.T, user *DBUser, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "user ID not returned from user creation")
			},
		},
		{
			name: "invalid_uuid_format",
			user: &DBUser{
				FirstName: "Test",
				LastName:  "User",
			},
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
			},
			wantErr: true,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
						return map[string]interface{}{
							"id":     "invalid-uuid",
							"origid": int64(12345),
						}, nil
					},
				}
			},
			verifyFn: func(t *testing.T, user *DBUser, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "failed to parse user ID as UUID")
			},
		},
		{
			name: "invalid_origid_type",
			user: &DBUser{
				FirstName: "Test",
				LastName:  "User",
			},
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
			},
			wantErr: true,
			setupFn: func() *MockDatabaseExecutor {
				userID := uuid.New()
				return &MockDatabaseExecutor{
					QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
						return map[string]interface{}{
							"id":     userID.String(),
							"origid": "invalid-origid", // Invalid type
						}, nil
					},
				}
			},
			verifyFn: func(t *testing.T, user *DBUser, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "user OrigID not returned from user creation")
			},
		},
		{
			name: "query_error",
			user: &DBUser{
				FirstName: "Test",
				LastName:  "User",
			},
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
			},
			wantErr: true,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
						return nil, assert.AnError
					},
				}
			},
			verifyFn: func(t *testing.T, user *DBUser, err error) {
				assert.Error(t, err)
			},
		},
		{
			name: "auth_method_insert_error",
			user: &DBUser{
				FirstName: "Test",
				LastName:  "User",
			},
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
			},
			wantErr: true,
			setupFn: func() *MockDatabaseExecutor {
				userID := uuid.New()
				return &MockDatabaseExecutor{
					QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
						return map[string]interface{}{
							"id":     userID.String(),
							"origid": int64(12345),
						}, nil
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return nil, assert.AnError // Auth method insert fails
					},
				}
			},
			verifyFn: func(t *testing.T, user *DBUser, err error) {
				assert.Error(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockDB := tt.setupFn()

			// Test
			err := createUserAndAuthMethod(mockDB, tt.user, tt.oidcClaims)

			// Verify
			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErr != nil {
					assert.ErrorIs(t, err, tt.expectedErr)
				}
			} else {
				assert.NoError(t, err)
			}

			// Additional verification if provided
			if tt.verifyFn != nil {
				tt.verifyFn(t, tt.user, err)
			}
		})
	}
}

func TestLookupUserByOIDCSubject(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		issuer      string
		subject     string
		expected    *DBUser
		expectedErr error
		wantErr     bool
		setupFn     func() *MockDatabaseExecutor
	}{
		{
			name:    "user_found_successfully",
			issuer:  "test-issuer",
			subject: "test-subject",
			expected: &DBUser{
				Username:               "testuser",
				Email:                  "<EMAIL>",
				FirstName:              "Test",
				LastName:               "User",
				OrganizationIdentifier: "org-123",
			},
			wantErr: false,
			setupFn: func() *MockDatabaseExecutor {
				userID := uuid.New()
				return &MockDatabaseExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						user := dest.(*DBUser)
						user.ID = userID
						user.OrigID = 12345
						user.Username = "testuser"
						user.Email = "<EMAIL>"
						user.FirstName = "Test"
						user.LastName = "User"
						user.OrganizationIdentifier = "org-123"
						return nil
					},
				}
			},
		},
		{
			name:     "no_user_found",
			issuer:   "test-issuer",
			subject:  "test-subject",
			expected: nil,
			wantErr:  false,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return sql.ErrNoRows
					},
				}
			},
		},
		{
			name:        "database_error",
			issuer:      "test-issuer",
			subject:     "test-subject",
			expected:    nil,
			expectedErr: ErrUserLookupFailed,
			wantErr:     true,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return assert.AnError
					},
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockDB := tt.setupFn()

			// Test
			result, err := lookupUserByOIDCSubject(mockDB, tt.issuer, tt.subject)

			// Verify
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.expectedErr != nil {
					assert.ErrorIs(t, err, tt.expectedErr)
				}
			} else {
				assert.NoError(t, err)
				if tt.expected == nil {
					assert.Nil(t, result)
				} else {
					assert.NotNil(t, result)
					assert.Equal(t, tt.expected.Username, result.Username)
					assert.Equal(t, tt.expected.Email, result.Email)
					assert.Equal(t, tt.expected.FirstName, result.FirstName)
					assert.Equal(t, tt.expected.LastName, result.LastName)
					assert.Equal(t, tt.expected.OrganizationIdentifier, result.OrganizationIdentifier)
				}
			}
		})
	}
}

func TestCreateUserPermissions(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		userID      uuid.UUID
		expected    *jwttokens.UserPermissions
		expectedErr error
		wantErr     bool
		setupFn     func() *MockDatabaseExecutor
	}{
		{
			name:   "successful_permissions_creation",
			userID: uuid.New(),
			expected: &jwttokens.UserPermissions{
				SoftwareGateway: []jwttokens.UserSoftwareGatewayAccess{},
				Device:          []jwttokens.UserDeviceAccess{},
			},
			wantErr: false,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Simulate empty permissions
						return nil
					},
				}
			},
		},
		{
			name:        "database_error",
			userID:      uuid.New(),
			expected:    nil,
			expectedErr: ErrPermissionsFailed,
			wantErr:     true,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						return assert.AnError
					},
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockDB := tt.setupFn()

			// Test
			result, err := createUserPermissions(mockDB, tt.userID)

			// Verify
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.expectedErr != nil {
					assert.ErrorIs(t, err, tt.expectedErr)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Empty(t, result.SoftwareGateway)
				assert.Empty(t, result.Device)
			}
		})
	}
}

func TestPersistTokenToDB(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		userID      uuid.UUID
		jwt         string
		expiresAt   time.Time
		expectedErr error
		wantErr     bool
		setupFn     func() *MockDatabaseExecutor
	}{
		{
			name:      "successful_token_persistence",
			userID:    uuid.New(),
			jwt:       "test-jwt-token",
			expiresAt: time.Now().Add(time.Hour),
			wantErr:   false,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return &mockResult{rowsAffected: 1}, nil
					},
				}
			},
		},
		{
			name:        "database_error",
			userID:      uuid.New(),
			jwt:         "test-jwt-token",
			expiresAt:   time.Now().Add(time.Hour),
			expectedErr: ErrTokenPersistence,
			wantErr:     true,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return nil, assert.AnError
					},
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockDB := tt.setupFn()

			// Test
			err := persistTokenToDB(mockDB, tt.userID, tt.jwt, tt.expiresAt)

			// Verify
			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErr != nil {
					assert.ErrorIs(t, err, tt.expectedErr)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGetOrCreateOIDCUser_ErrorScenarios(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		oidcClaims  *OIDCClaims
		expectedErr error
		wantErr     bool
		setupFn     func() *MockDatabaseExecutor
	}{
		{
			name: "create_user_error",
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
				Name:   "New User",
			},
			wantErr: true,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Simulate no user found by returning sql.ErrNoRows
						return sql.ErrNoRows
					},
					QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
						return nil, assert.AnError // Create user fails
					},
				}
			},
		},
		{
			name: "update_last_login_error",
			oidcClaims: &OIDCClaims{
				Sub:    "test-subject",
				Issuer: "test-issuer",
				Email:  "<EMAIL>",
			},
			wantErr: true,
			setupFn: func() *MockDatabaseExecutor {
				return &MockDatabaseExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						user := dest.(*DBUser)
						user.ID = uuid.New()
						user.OrigID = 12345
						user.Username = "existinguser"
						user.Email = "<EMAIL>"
						user.FirstName = "Existing"
						user.LastName = "User"
						user.OrganizationIdentifier = "org-123"
						return nil
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return nil, assert.AnError // Update fails
					},
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockDB := tt.setupFn()

			// Test
			result, err := getOrCreateOIDCUser(mockDB, tt.oidcClaims)

			// Verify
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.expectedErr != nil {
					assert.ErrorIs(t, err, tt.expectedErr)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

func TestUpdateLastLogin_ErrorScenarios(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name              string
		userID            uuid.UUID
		expectedCallCount int
		wantErr           bool
		setupFn           func() (*MockDatabaseExecutor, *int)
	}{
		{
			name:              "first_update_error",
			userID:            uuid.New(),
			expectedCallCount: 1,
			wantErr:           true,
			setupFn: func() (*MockDatabaseExecutor, *int) {
				callCount := 0
				mockDB := &MockDatabaseExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						callCount++
						if callCount == 1 {
							return nil, assert.AnError // First update fails
						}
						return &mockResult{rowsAffected: 1}, nil
					},
				}
				return mockDB, &callCount
			},
		},
		{
			name:              "second_update_error",
			userID:            uuid.New(),
			expectedCallCount: 2,
			wantErr:           true,
			setupFn: func() (*MockDatabaseExecutor, *int) {
				callCount := 0
				mockDB := &MockDatabaseExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						callCount++
						if callCount == 2 {
							return nil, assert.AnError // Second update fails
						}
						return &mockResult{rowsAffected: 1}, nil
					},
				}
				return mockDB, &callCount
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockDB, callCount := tt.setupFn()

			// Test
			err := updateLastLogin(mockDB, tt.userID)

			// Verify
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectedCallCount, *callCount, "Should call expected number of updates")
		})
	}
}
