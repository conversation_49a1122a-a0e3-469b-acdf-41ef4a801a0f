package routes

import (
	"net/http"
	"testing"

	"github.com/gorilla/mux"
)

// helper to compare slices ignoring order
func equal(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	m := make(map[string]int, len(a))
	for _, x := range a {
		m[x]++
	}
	for _, x := range b {
		if m[x] == 0 {
			return false
		}
		m[x]--
	}
	return true
}

func TestNewRouter_RegistersAllExpectedRoutes(t *testing.T) {
	// You can pass nil here as long as your middleware constructors
	// don't panic until the route is actually invoked.
	r := NewRouter(nil, nil)

	// map[path]methods
	expected := map[string][]string{
		"/":                                  {http.MethodGet},
		"/data/v2/device":                    {http.MethodGet},
		"/data/v2/fault":                     {http.MethodGet},
		"/user/v2/profile":                   {http.MethodGet},
		"/user/v2/account/close":             {http.MethodPost},
		"/user/v2/account/notifications":     {http.MethodPost},
		"/user/v3/authenticate":              {http.MethodPost},
		"/user/v3/instruction":               {http.MethodPost},
		"/api/v3/gateway/authenticate":       {http.MethodPost},
		"/api/v3/gateway/ingest":             {http.MethodPost},
		"/api/v3/gateway/update":             {http.MethodPost},
		"/api/v3/data/device":                {http.MethodGet},
		"/api/v3/data/fault":                 {http.MethodGet},
		"/api/v3/user/account/close":         {http.MethodPost},
		"/api/v3/user/account/notifications": {http.MethodPost},
		"/api/v3/user/authenticate":          {http.MethodPost},
		"/api/v3/user/instruction":           {http.MethodPost},
		"/api/v3/user/oidc/password-grant":   {http.MethodPost},
		"/api/v3/user/profile":               {http.MethodGet},
	}

	// collect from the router
	got := map[string][]string{}
	err := r.Walk(func(route *mux.Route, _ *mux.Router, _ []*mux.Route) error {
		path, err := route.GetPathTemplate()
		if err != nil {
			// skip routes without a path template (e.g. subrouters, middleware)
			return nil
		}
		methods, _ := route.GetMethods()
		got[path] = methods
		return nil
	})
	if err != nil {
		t.Fatalf("router.Walk: %v", err)
	}

	// now compare
	for path, wantMethods := range expected {
		gotMethods, ok := got[path]
		if !ok {
			t.Errorf("missing route %q", path)
			continue
		}
		if !equal(gotMethods, wantMethods) {
			t.Errorf("route %q: methods = %v, want %v", path, gotMethods, wantMethods)
		}
	}
}