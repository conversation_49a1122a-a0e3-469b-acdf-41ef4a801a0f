# ------------------------------------------------------------------
# Stage 1: Builder (compiles broker only)
# ------------------------------------------------------------------
FROM golang:1.24 AS builder
WORKDIR /app

# Accept build arguments for git branch and tag information
ARG GIT_BRANCH=unknown
ARG GIT_TAG=untagged

# Copy service and shared libs
COPY microservices/broker .
COPY shared /shared

# Ensure the setup script is executable.
RUN chmod +x /shared/setup_shared.sh

# Wire up shared modules + deps
RUN /shared/setup_shared.sh && go mod tidy && go mod download

# Build metadata
RUN echo "${GIT_BRANCH}-${GIT_TAG}" > identifier.txt

# Compile broker
RUN go build -o broker


# ------------------------------------------------------------------
# Stage 2: Production (distroless)
# ------------------------------------------------------------------
FROM gcr.io/distroless/base-debian12 AS prod
WORKDIR /prod
USER nonroot:nonroot

# Binary + metadata
COPY --from=builder /app/broker .
COPY --from=builder /app/identifier.txt .

# Copy committed OpenAPI and Swagger UI (with swagger-initializer.js committed)
COPY microservices/broker/openapi /openapi

CMD ["./broker"]


# ------------------------------------------------------------------
# Stage 3: Development (regenerates docs on container start)
# ------------------------------------------------------------------
FROM golang:1.24 AS dev
WORKDIR /app

# Tooling for dev doc generation
RUN go install github.com/swaggo/swag/cmd/swag@v1.16.6

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Expect a volume mount for microservices/broker -> /app
# CMD:
# 1. setup shared modules
# 2. tidy & download deps
# 3. swag init -> docs/swagger.json (entry is ./main.go)
# 4. openapi-filter -> openapi/broker.yaml (committed by you)
# 5. start the app
CMD sh -c '\
  /shared/setup_shared.sh && \
  go mod tidy && go mod download && \
  echo "Waiting for Keycloak to be ready..." && \
  until curl -f http://keycloak:8080/realms/onramp-dev >/dev/null 2>&1; do \
    echo "Keycloak not ready, waiting 2 seconds..." && \
    sleep 2; \
  done && \
  echo "Keycloak is ready!" && \
  swag init -g ./main.go -o ./docs --parseDependency --parseInternal && \
  (cd /tools && go mod tidy && go mod download) && \
  (cd /tools/openapi-filter && \
    echo "Generating environment-specific OpenAPI documentation..." && \
    go run . -in /app/docs/swagger.json -env dev -out /app/openapi/broker-dev.yaml && \
    go run . -in /app/docs/swagger.json -env dev -scheme http -out /app/openapi/broker-dev-local.yaml && \
    go run . -in /app/docs/swagger.json -env qa -out /app/openapi/broker-qa.yaml && \
    go run . -in /app/docs/swagger.json -env prod -out /app/openapi/broker.yaml && \
    go run . -in /app/docs/swagger.json -env sandbox -out /app/openapi/broker-sandbox.yaml && \
    echo "Generated documentation for all environments (including local dev)") && \
  go run . \
'
