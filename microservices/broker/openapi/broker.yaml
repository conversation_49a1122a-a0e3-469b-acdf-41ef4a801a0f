basePath: /api
components: {}
definitions:
    authenticate.authResponse:
        properties:
            code:
                description: HTTP status code
                example: 200
                type: integer
            data:
                allOf:
                    - $ref: '#/definitions/authenticate.dataUserResponsePayload'
                description: Authentication data containing user info and JWT token
            message:
                description: Success message
                example: Authentication successful
                type: string
            status:
                description: Response status
                example: success
                type: string
        type: object
    authenticate.credentials:
        properties:
            password:
                description: User's password
                example: securePassword123
                type: string
            username:
                description: User's username or email address
                example: <EMAIL>
                type: string
        required:
            - password
            - username
        type: object
    authenticate.dataUserResponsePayload:
        properties:
            token:
                description: JWT token for authentication
                example: eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
                type: string
            user:
                allOf:
                    - $ref: '#/definitions/authenticate.userDetailRecord'
                description: User information
        type: object
    authenticate.requestBody:
        properties:
            token:
                description: Authentication token (can be empty string for initial setup)
                example: abc123token
                type: string
        required:
            - token
        type: object
    authenticate.userDetailRecord:
        properties:
            api_key:
                description: API key (legacy field)
                example: api-key-789
                type: string
            organization_id:
                description: Organization UUID the user belongs to
                example: org-uuid-456
                type: string
            role:
                description: User's role within the organization
                example: admin
                type: string
            user_id:
                description: Unique user identifier
                example: 12345
                type: integer
            user_identifier:
                description: User UUID identifier
                example: user-uuid-123
                type: string
            username:
                description: Username/email used for login
                example: <EMAIL>
                type: string
        type: object
    device.channelStatus:
        properties:
            green:
                description: Green channel status array (true = on)
                items:
                    type: boolean
                type: array
            red:
                description: Red channel status array (true = on)
                items:
                    type: boolean
                type: array
            yellow:
                description: Yellow channel status array (true = on)
                items:
                    type: boolean
                type: array
        type: object
    device.dataPayload:
        properties:
            device_id:
                description: Numeric device identifier
                example: 12345
                type: integer
            device_identifier:
                description: Device UUID or identifier string
                example: device-uuid-123
                type: string
            device_info:
                allOf:
                    - $ref: '#/definitions/device.deviceMetadata'
                description: Device hardware and software metadata
            location:
                allOf:
                    - $ref: '#/definitions/device.location'
                description: Geographic location information
            status:
                allOf:
                    - $ref: '#/definitions/device.deviceStatus'
                description: Current device status and fault information
        type: object
    device.deviceMetadata:
        properties:
            application_version:
                description: Application software version
                example: ""
                type: string
            comm_version:
                description: Communication protocol version
                example: 3.0.1
                type: string
            device_type:
                description: Type classification of the device
                example: EDI_LEGACY
                type: string
            firmware_type:
                description: Firmware type classification
                example: ""
                type: string
            firmware_version:
                description: Firmware version number
                example: 1.5.2
                type: string
            ip_address:
                description: Device IP address
                example: *************
                type: string
            manufacturer:
                description: Device manufacturer name
                example: EDI
                type: string
            model:
                description: Device model identifier
                example: Model-X1
                type: string
            port:
                description: Device communication port
                example: "8080"
                type: string
            rms_engine_firmware_type:
                description: RMS engine firmware type
                example: 1.0.0
                type: string
            rms_engine_firmware_version:
                description: RMS engine firmware version
                example: 4.2.1
                type: string
            serial_number:
                description: Device serial number (for EDI Next Gen devices)
                example: 1234567890ABCD
                type: string
            user_assigned_device_id:
                description: User-defined device identifier
                example: DEVICE001
                type: string
            user_assigned_device_name:
                description: User-friendly device name
                example: Main St
                type: string
        type: object
    device.deviceStatus:
        properties:
            faulted_channel_status:
                allOf:
                    - $ref: '#/definitions/device.channelStatus'
                description: The state of the channels of the device during the last fault
            heartbeat_received_utc:
                description: Timestamp of last heartbeat received
                example: "2024-01-15T10:30:00Z"
                type: string
            last_fault_reason:
                description: Description of the most recent fault
                example: Communication timeout
                type: string
            last_fault_uploaded_utc:
                description: Timestamp when the last fault was reported
                example: "2024-01-15T09:15:00Z"
                type: string
            log_uploaded_utc:
                description: Timestamp of last log upload
                example: "2024-01-15T10:25:00Z"
                type: string
            state:
                description: Current device state (nofault, fault, nevercomm, etc.)
                example: nofault
                type: string
        type: object
    device.location:
        properties:
            latitude:
                description: Device latitude coordinate
                example: "40.7128"
                type: string
            longitude:
                description: Device longitude coordinate
                example: "-74.0060"
                type: string
        type: object
    instruction.userInstructionRequest:
        properties:
            device_id:
                description: Device identifier (integer ID or UUID string format)
                items:
                    type: integer
                type: array
            instruction:
                description: Instruction command to send to device
                example: get_device_logs
                type: string
        required:
            - device_id
            - instruction
        type: object
    notifications.Request:
        properties:
            notification_sms_enabled:
                description: Enable or disable SMS notifications for the user
                example: true
                type: boolean
        required:
            - notification_sms_enabled
        type: object
    notifications.Response:
        properties:
            notification_sms_enabled:
                description: Current SMS notification setting after update
                example: true
                type: boolean
            trace_id:
                description: Request trace identifier for debugging
                example: trace-abc-456
                type: string
            user_id:
                description: User's unique identifier
                example: user-uuid-123
                type: string
        type: object
    oidc.AuthResponse:
        properties:
            token:
                type: string
            user:
                $ref: '#/definitions/synapse-its_com_broker_api_handlers_v3_user_oidc.UserInfo'
        type: object
    oidc.PasswordGrantRequest:
        properties:
            password:
                type: string
            username:
                type: string
        type: object
    profile.AppVersion:
        properties:
            android:
                type: string
            ios:
                type: string
            windows:
                type: string
        type: object
    profile.AppVersions:
        properties:
            EDIFieldServiceApp:
                properties:
                    dev:
                        $ref: '#/definitions/profile.AppVersion'
                    notpublished:
                        $ref: '#/definitions/profile.AppVersion'
                    published:
                        $ref: '#/definitions/profile.AppVersion'
                type: object
        type: object
    profile.ResponsePayload:
        properties:
            app_version:
                $ref: '#/definitions/profile.AppVersions'
            user_profile:
                $ref: '#/definitions/profile.UserProfileRecord'
        type: object
    profile.UserProfileRecord:
        properties:
            description:
                type: string
            email:
                type: string
            first_name:
                type: string
            last_login:
                type: string
            last_name:
                type: string
            mobile:
                type: string
            notification_sms_enabled:
                type: boolean
            user_id:
                type: integer
            user_identifier:
                type: string
            username:
                type: string
        type: object
    shared.BadRequestResponse:
        properties:
            code:
                description: HTTP status code
                example: 400
                type: integer
            data:
                description: Always null for error responses
            message:
                description: Error message
                example: Bad Request
                type: string
            status:
                description: Response status
                example: error
                type: string
        type: object
    shared.InternalServerErrorResponse:
        properties:
            code:
                description: HTTP status code
                example: 500
                type: integer
            data:
                description: Always null for error responses
            message:
                description: Error message
                example: Internal Server Error
                type: string
            status:
                description: Response status
                example: error
                type: string
        type: object
    shared.UnauthorizedResponse:
        properties:
            code:
                description: HTTP status code
                example: 401
                type: integer
            data:
                description: Always null for error responses
            message:
                description: Error message
                example: Unauthorized
                type: string
            status:
                description: Response status
                example: error
                type: string
        type: object
    softwaregateway.CloudSettings:
        properties:
            api_key:
                description: the api key used by the software gateway to communicate with the cloud
                type: string
            token:
                description: the rotating token used by the software gateway to authenticate with the cloud
                type: string
        type: object
    softwaregateway.DeviceSettings:
        properties:
            device_id:
                description: uniquely identifies this device amongst all possible devices
                type: string
            device_type:
                description: the type of device (EDI_LEGACY, EDI_NEXT_GEN, etc)
                type: string
            enable_realtime:
                description: flag indiciating whether or not to send realtime channel state to firebase
                type: string
            flush_connection_ms:
                description: the amount of time to wait to flush all bytes from the tcp connection for the device
                type: string
            ip_address:
                description: the device ip address
                type: string
            latitude:
                description: the device latitude
                type: string
            longitude:
                description: the device longitude
                type: string
            port:
                description: the device port
                type: string
        type: object
    softwaregateway.GlobalSettings:
        properties:
            aws:
                allOf:
                    - $ref: '#/definitions/softwaregateway.CloudSettings'
                description: all cloud settings
            devices:
                description: device info for all devices the software gateway communicates with
                items:
                    $ref: '#/definitions/softwaregateway.DeviceSettings'
                type: array
            gateway:
                additionalProperties: true
                description: all software gateway settings (dynamic)
                type: object
            google:
                description: google settings (aka firebase connection info) base 64 end encrypted
                type: string
            organization_id:
                description: the organization the software gateway is assigned to
                type: string
            public_key:
                description: the public key used to authenticate the signature of a jwt
                type: string
        type: object
    softwaregateway.OnDemandPayload:
        properties:
            device_identifier:
                type: string
            instruction:
                type: string
        type: object
    softwaregateway.SoftwareGatewayUpdateResponse:
        properties:
            config_update_available:
                type: string
            instructions:
                items:
                    $ref: '#/definitions/softwaregateway.OnDemandPayload'
                type: array
        type: object
    synapse-its_com_broker_api_handlers_v3_user_oidc.UserInfo:
        properties:
            api_key:
                type: string
            organization_id:
                type: string
            user_id:
                type: integer
            user_identifier:
                type: string
            username:
                type: string
        type: object
    update.requestBody:
        properties:
            token:
                description: Authentication token for gateway verification
                example: abc123token
                type: string
        required:
            - token
        type: object
info:
    contact: {}
    description: Public endpoints for third-party integrations.
    title: Broker API
    version: "1.0"
openapi: ""
paths: {}
schemes:
    - https
securityDefinitions:
    JWTAuth:
        in: header
        name: jwt-token
        type: apiKey
swagger: "2.0"
