Title
As a backend, I need to be able to authenticate a user using OIDC in broker

Description
After discussion, we will not be allowed to process username/password authentication for the OIDC provider (the ROPC flow). 

Instead, the FSA will do the authentication directly with the IdP and receive the token. The app will send the token to <PERSON><PERSON><PERSON> which will be validated using the oidc package and we will return a JWT for the user. FSA Oauth example: 
Oauth Example - Jira Mobile App 

Endpoints:

1. GET /api/v3/user/oidc/start - This is where the app will get OIDC information and a state and a nonce

There will be no request body. Anyone can hit this endpoint.

The handler will generate a random state and nonce 32 character string and store this value into redis as key oauth_state:<state> with value <nonce> and a 10 minute TTL. The cloud will use it to prevent replay attacks.

The handler will obtain the SYNAPSE_OIDC_ISSUER_URL and SYNAPSE_OIDC_CLIENT_ID to include in the response.

The response body will contain:

{
  "state": "HkS6...Gq", // State key in redis
  "nonce": "kYxP...9Q", // Nonce value in redis
  "issuer_url": "http://keycloak:8080/realms/onramp-dev", // The environmental variable
  "client_id": "onramp-dev-client" // The environmental varaible
}
2. POST /api/v3/user/oidc/token-exchange - This is where the app will exchange the OIDC token for the JWT generated by a successful authentication.

The request body will contain:

{
  "client_id": "onramp-dev-client", // This will match the SYNAPSE_OIDC_CLIENT_ID env variable 
  "id_token": "eyJhbGciOiJSUzI1Ni...", // This will be token provided by the OIDC 
  "state": "HkS6...Gq" // This will be a replay of the first endpoint 
}
 

The handler will verify the request is valid.

The handler will get and delete the redis key oauth_state:<state> and will use the <nonce> later. We are immediately removing this so that the state cannot be reused.

The client_id will be verified it matches the config. Then the token_id verified using the oidc library. 

The claims will be extracted and we will check that the <nonce> matches the “Nonce” within the claims.

The handler will match the user account and return the current structure of the JWT response in the username/password flow.