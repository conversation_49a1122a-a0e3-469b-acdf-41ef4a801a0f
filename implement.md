# OIDC Token Exchange Implementation Guide

## Overview
This document provides a detailed step-by-step implementation guide for migrating from the Resource Owner Password Credentials (ROPC) flow to a new OIDC token exchange flow where the FSA (Frontend Single-page Application) authenticates directly with the Identity Provider (IdP) and exchanges the received token for a JWT from the Broker service.

## Current State Analysis
- **Current Implementation**: ROPC flow in `/microservices/broker/api/handlers/v3/user/oidc/` processes username/password authentication
- **Current Endpoint**: `POST /api/v3/user/oidc/password-grant` 
- **Current Flow**: Broker receives username/password → exchanges with IdP → returns JWT
- **Infrastructure**: Redis available via `shared/connect/redis.go`, OIDC config via `shared/api/oidc/config.go`

## New Requirements
- **New Flow**: FSA authenticates with IdP → receives OIDC token → exchanges with <PERSON>roker for JWT
- **New Endpoints**: 
  1. `GET /api/v3/user/oidc/start` - Get OIDC configuration and state/nonce
  2. `POST /api/v3/user/oidc/token-exchange` - Exchange OIDC token for JWT
- **Security**: State/nonce validation, replay attack prevention, 10-minute TTL

## Implementation Steps

### Step 1: Create New Schema Definitions
**File**: `microservices/broker/api/handlers/v3/user/oidc/schemas.go`

Add new request/response schemas:

```go
// OIDCStartResponse represents the response for the OIDC start endpoint
type OIDCStartResponse struct {
    State     string `json:"state"`      // State key for Redis
    Nonce     string `json:"nonce"`      // Nonce value for validation
    IssuerURL string `json:"issuer_url"` // OIDC issuer URL
    ClientID  string `json:"client_id"`  // OIDC client ID
}

// TokenExchangeRequest represents the request for token exchange
type TokenExchangeRequest struct {
    ClientID string `json:"client_id,required"` // Must match SYNAPSE_OIDC_CLIENT_ID
    IDToken  string `json:"id_token,required"`  // OIDC ID token from FSA
    State    string `json:"state,required"`     // State from start endpoint
}
```

### Step 2: Add Redis State Management
**File**: `microservices/broker/api/handlers/v3/user/oidc/helper.go`

Add Redis state management functions:

```go
import (
    "crypto/rand"
    "encoding/hex"
    "fmt"
    "time"
    "github.com/redis/go-redis/v9"
    "synapse-its.com/shared/connect"
)

const (
    OAuthStateKeyPrefix = "oauth_state:"
    StateNonceTTL      = 10 * time.Minute
    StateLength        = 32
    NonceLength        = 32
)

// generateRandomString generates a cryptographically secure random string
func generateRandomString(length int) (string, error) {
    bytes := make([]byte, length/2) // hex encoding doubles the length
    if _, err := rand.Read(bytes); err != nil {
        return "", err
    }
    return hex.EncodeToString(bytes), nil
}

// storeStateNonce stores state-nonce pair in Redis with TTL
func storeStateNonce(ctx context.Context, redisClient *redis.Client, state, nonce string) error {
    key := fmt.Sprintf("%s%s", OAuthStateKeyPrefix, state)
    return redisClient.Set(ctx, key, nonce, StateNonceTTL).Err()
}

// getAndDeleteStateNonce retrieves and immediately deletes state-nonce pair from Redis
func getAndDeleteStateNonce(ctx context.Context, redisClient *redis.Client, state string) (string, error) {
    key := fmt.Sprintf("%s%s", OAuthStateKeyPrefix, state)
    
    // Use pipeline for atomic get-and-delete
    pipe := redisClient.Pipeline()
    getCmd := pipe.Get(ctx, key)
    pipe.Del(ctx, key)
    
    _, err := pipe.Exec(ctx)
    if err != nil {
        return "", err
    }
    
    return getCmd.Result()
}
```

### Step 3: Implement OIDC Start Handler
**File**: `microservices/broker/api/handlers/v3/user/oidc/handler.go`

Add the start endpoint handler:

```go
// HandleOIDCStartWithDeps handles the OIDC start endpoint with dependency injection
func HandleOIDCStartWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()
        logger := logger.WithContext(ctx)
        
        // Generate state and nonce
        state, err := generateRandomString(StateLength)
        if err != nil {
            logger.Errorf("Failed to generate state: %v", err)
            response.CreateInternalServerErrorResponse(w)
            return
        }
        
        nonce, err := generateRandomString(NonceLength)
        if err != nil {
            logger.Errorf("Failed to generate nonce: %v", err)
            response.CreateInternalServerErrorResponse(w)
            return
        }
        
        // Get Redis connection
        connections, err := deps.Service.DBProvider(ctx)
        if err != nil {
            logger.Errorf("Failed to get database connections: %v", err)
            response.CreateInternalServerErrorResponse(w)
            return
        }
        
        // Store state-nonce pair in Redis
        err = storeStateNonce(ctx, connections.Redis, state, nonce)
        if err != nil {
            logger.Errorf("Failed to store state-nonce in Redis: %v", err)
            response.CreateInternalServerErrorResponse(w)
            return
        }
        
        // Get OIDC configuration
        issuerURL := os.Getenv("SYNAPSE_OIDC_ISSUER_URL")
        clientID := os.Getenv("SYNAPSE_OIDC_CLIENT_ID")
        
        if issuerURL == "" || clientID == "" {
            logger.Error("OIDC configuration missing - SYNAPSE_OIDC_ISSUER_URL or SYNAPSE_OIDC_CLIENT_ID not set")
            response.CreateInternalServerErrorResponse(w)
            return
        }
        
        // Create response
        startResponse := &OIDCStartResponse{
            State:     state,
            Nonce:     nonce,
            IssuerURL: issuerURL,
            ClientID:  clientID,
        }
        
        response.CreateSuccessResponse(startResponse, w)
    }
}
```

### Step 4: Implement Token Exchange Handler
**File**: `microservices/broker/api/handlers/v3/user/oidc/handler.go`

Add the token exchange endpoint handler:

```go
// HandleTokenExchangeWithDeps handles the OIDC token exchange endpoint
func HandleTokenExchangeWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()
        logger := logger.WithContext(ctx)
        
        // Parse request body
        var req TokenExchangeRequest
        if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
            logger.Errorf("Failed to parse token exchange request: %v", err)
            response.CreateBadRequestResponse(w)
            return
        }
        
        // Validate required fields
        if req.ClientID == "" || req.IDToken == "" || req.State == "" {
            logger.Error("Missing required fields in token exchange request")
            response.CreateBadRequestResponse(w)
            return
        }
        
        // Process token exchange
        authResp, err := deps.processTokenExchangeLogic(ctx, req)
        if err != nil {
            logger.Errorf("Token exchange failed: %v", err)
            response.CreateUnauthorizedResponse(w)
            return
        }
        
        // Return JWT token response
        response.CreateAuthSuccessResponse(authResp, w)
    }
}
```

### Step 5: Implement Token Exchange Logic
**File**: `microservices/broker/api/handlers/v3/user/oidc/handler.go`

Add the core token exchange logic:

```go
// processTokenExchangeLogic handles the token exchange business logic
func (deps HandlerDeps) processTokenExchangeLogic(ctx context.Context, req TokenExchangeRequest) (*AuthResponse, error) {
    // Verify client ID matches configuration
    expectedClientID := os.Getenv("SYNAPSE_OIDC_CLIENT_ID")
    if req.ClientID != expectedClientID {
        logger.Errorf("Client ID mismatch: expected %s, got %s", expectedClientID, req.ClientID)
        return nil, ErrInvalidClientID
    }
    
    // Get database connections
    connections, err := deps.Service.DBProvider(ctx)
    if err != nil {
        logger.Errorf("Failed to get database connections: %v", err)
        return nil, ErrDatabaseConnection
    }
    
    // Get and delete state-nonce pair from Redis (prevents replay attacks)
    expectedNonce, err := getAndDeleteStateNonce(ctx, connections.Redis, req.State)
    if err != nil {
        logger.Errorf("Failed to retrieve state-nonce from Redis: %v", err)
        return nil, ErrInvalidState
    }
    
    // Get OIDC configuration
    isDev := strings.Contains(strings.ToLower(os.Getenv("ENVIRONMENT")), "dev")
    oidcConfig := getConfig(isDev)
    if oidcConfig == nil {
        logger.Error("OIDC configuration not available")
        return nil, ErrOIDCConfigMissing
    }
    
    // Verify the ID Token
    idToken, err := deps.TokenExchanger.VerifyIDToken(ctx, oidcConfig, req.IDToken)
    if err != nil {
        logger.Errorf("Failed to verify ID token: %v", err)
        return nil, ErrInvalidIDToken
    }
    
    // Extract claims from ID token
    claims, err := deps.TokenExchanger.ExtractClaims(idToken)
    if err != nil {
        logger.Errorf("Failed to extract claims from ID token: %v", err)
        return nil, ErrClaimsExtraction
    }
    
    // Verify nonce matches
    claimsNonce, ok := claims["nonce"].(string)
    if !ok || claimsNonce != expectedNonce {
        logger.Errorf("Nonce mismatch: expected %s, got %s", expectedNonce, claimsNonce)
        return nil, ErrNonceMismatch
    }
    
    // Extract and validate OIDC claims
    oidcClaims, err := extractOIDCClaims(claims)
    if err != nil {
        logger.Errorf("Failed to extract OIDC claims: %v", err)
        return nil, ErrClaimsExtraction
    }
    
    // Continue with existing user lookup/creation and JWT generation logic
    // (reuse existing logic from processPasswordGrantLogic)
    return deps.createJWTFromOIDCClaims(ctx, oidcClaims)
}
```

### Step 6: Add New Error Types
**File**: `microservices/broker/api/handlers/v3/user/oidc/errors.go`

Add new error definitions:

```go
var (
    // Existing errors...
    
    // New errors for token exchange flow
    ErrInvalidState     = errors.New("invalid or expired state")
    ErrInvalidClientID  = errors.New("invalid client ID")
    ErrNonceMismatch    = errors.New("nonce mismatch")
    ErrStateGeneration  = errors.New("failed to generate state")
    ErrNonceGeneration  = errors.New("failed to generate nonce")
    ErrRedisOperation   = errors.New("Redis operation failed")
)
```

### Step 7: Update Service Dependencies
**File**: `microservices/broker/api/handlers/v3/user/oidc/helper.go`

Update the Service struct to include Redis access:

```go
// Service handles OIDC authentication business logic for broker
type Service struct {
    DBProvider             func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
    JwtCreator             func(string, time.Duration, jwttokens.UserPermissions) (string, time.Time, error)
    TokenPersister         func(connect.DatabaseExecutor, uuid.UUID, string, time.Time) error
    UserPermissionsCreator func(connect.DatabaseExecutor, uuid.UUID) (*jwttokens.UserPermissions, error)
    GetOrCreateOIDCUser    func(connect.DatabaseExecutor, *OIDCClaims) (*DBUser, error)
    // Note: Redis access is available through DBProvider().Redis
}
```

### Step 8: Register New Routes
**File**: `microservices/broker/api/routes/routes.go`

Add the new endpoints to the router:

```go
// Add these lines after the existing OIDC route (around line 66)
router.HandleFunc("/api/v3/user/oidc/start", V3UserOidc.OIDCStartHandler).Methods(http.MethodGet)
router.HandleFunc("/api/v3/user/oidc/token-exchange", V3UserOidc.TokenExchangeHandler).Methods(http.MethodPost)
```

### Step 9: Create Handler Instances
**File**: `microservices/broker/api/handlers/v3/user/oidc/handler.go`

Add handler instances at the end of the file:

```go
// Handler instances for the new endpoints
var OIDCStartHandler = HandleOIDCStartWithDeps(HandlerDeps{
    TokenExchanger: SharedOidc.NewOAuth2TokenExchanger(),
    Service:        NewService(),
})

var TokenExchangeHandler = HandleTokenExchangeWithDeps(HandlerDeps{
    TokenExchanger: SharedOidc.NewOAuth2TokenExchanger(),
    Service:        NewService(),
})
```

### Step 10: Update Connection Provider
**File**: `shared/connect/connections.go`

Ensure Redis is available in the connections struct (should already be available based on existing code).

## Testing Strategy

### Unit Tests
1. **State/Nonce Generation**: Test random string generation
2. **Redis Operations**: Test state storage and retrieval with TTL
3. **Token Validation**: Test ID token verification and claims extraction
4. **Error Handling**: Test all error scenarios (invalid state, expired nonce, etc.)

### Integration Tests
1. **Full Flow**: Test complete OIDC start → token exchange flow
2. **Security**: Test replay attack prevention
3. **TTL Expiration**: Test state expiration after 10 minutes

### Manual Testing
1. Use Postman or curl to test endpoints
2. Verify Redis state storage with Redis CLI
3. Test with actual OIDC provider (Keycloak)

## Security Considerations

1. **State Management**: State is immediately deleted after use to prevent replay attacks
2. **Nonce Validation**: Nonce from claims must match stored nonce
3. **TTL**: 10-minute expiration for state/nonce pairs
4. **Client ID Validation**: Strict validation against environment configuration
5. **Token Verification**: Full OIDC token verification using established libraries

## Migration Strategy

1. **Phase 1**: Implement new endpoints alongside existing ROPC flow
2. **Phase 2**: Update FSA to use new flow
3. **Phase 3**: Deprecate and remove ROPC endpoint after successful migration

## Environment Variables Required

- `SYNAPSE_OIDC_ISSUER_URL`: OIDC provider issuer URL
- `SYNAPSE_OIDC_CLIENT_ID`: OIDC client identifier
- `SYNAPSE_OIDC_CLIENT_SECRET`: OIDC client secret (for token verification)
- `MEMORYSTORE_HOST`: Redis host (already configured)
- `MEMORYSTORE_PORT`: Redis port (already configured)

## Next Steps

1. Implement the code changes as outlined above
2. Write comprehensive unit tests
3. Test with development environment
4. Update API documentation
5. Coordinate with frontend team for FSA integration
6. Plan migration timeline and rollback strategy
